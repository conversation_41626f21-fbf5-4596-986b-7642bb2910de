/*************************************************
  Copyright (C),  Embed-Te<PERSON> ,TianJin
  File name:      ebtNST112VxDrv.h
  Author:  		  Version:  V1.0      
  Description:    NST112 Functions  
  
   History:        
  1. Date:
     Author:
     Modification: 
    
*************************************************/
#ifndef _NST112_Drv_H
#define _NST112_Drv_H

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

STATUS nst112Read (UINT8 reg,UINT16 *buf);
STATUS nst112Write(UINT8 reg, UINT16 val);



STATUS nst112InitSet(UINT mode);
int environmentTemperature(void);
int environmentTemperatureExt(void);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* _NST112_Drv_H */
