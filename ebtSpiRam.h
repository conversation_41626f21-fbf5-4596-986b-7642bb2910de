/*******************************************************************************\
    SpiRam.c -- EmbedTec  driver  File for the FRAM write and read    
    File name: ebtSpiRam.h  
    
\********************************************************************************/

#ifndef __SPIRAM_DRV_H__
#define __SPIRAM_DRV_H__
#include <vxWorks.h>
#include <stdio.h>
#include <string.h>
#include <logLib.h>
#include <vxBusLib.h>
#include <semLib.h>
#include <taskLib.h>
#include <sysLib.h>
#include <tickLib.h>
#include <hwif/vxbus/vxBus.h>
#include <hwif/vxbus/hwConf.h>
#include <hwif/vxbus/vxbPlbLib.h>
#include <hwif/util/hwMemLib.h>
#include <hwif/util/vxbParamSys.h>

#include <hwif/vxbus/vxbSpiLib.h>

#include "vxbCy15bSpiRam.h"

int SpiRam_read_nvol(FLASH_ADDR_T start,FLASH_SIZE_T len, UINT8 *dst);
int SpiRam_write_nvol(FLASH_ADDR_T start,FLASH_SIZE_T len, UINT8 *src);
int SpiRam_read_vol(FLASH_ADDR_T start,FLASH_SIZE_T len, UINT8 *dst);
int SpiRam_write_vol(FLASH_ADDR_T start,FLASH_SIZE_T len, UINT8 *src);
int SpiRam_read(FLASH_ADDR_T start,FLASH_SIZE_T len, UINT8 *dst);
int SpiRam_write(FLASH_ADDR_T start,FLASH_SIZE_T len, UINT8 *src);
#endif
