/*************************************************
  Copyright (C),  Embed-Te<PERSON> ,TianJin
  File name:      ebtPcf8563Rtc.c
  Description:     pcf8563 RTC Functions  
 
*************************************************/


#include <vxWorks.h>
#include "ebtPcf8563Rtc.h"

/* debug */
#define PCF8563_DBG_ON
#ifdef PCF8563_DBG_ON
#define PCF8563_RTC_DBG(fmt...)	printf (fmt)
#else
#define PCF8563_RTC_DBG(fmt...)
#endif	/* PCF8563_DBG_ON */

#define PCF8563_RTC_NAME "PCF8563"

/*Control and status registers*/
#define PCF8563_CON_STA1_REG 0x00
#define PCF8563_CON_STA2_REG 0x01

/*Time and date registers*/
#define PCF8563_SEC_REG  0x02
#define PCF8563_MIN_REG 0x03
#define PCF8563_HOUR_REG 0x04
#define PCF8563_DAY_REG    0x05
#define PCF8563_WDAY_REG	0x06
#define PCF8563_MONTH_REG	0x07
#define PCF8563_YEAR_REG	0x08

/*Alarm registers*/
#define PCF8563_MIN_ALARM_REG 0x09
#define PCF8563_HOUR_ALARM_REG 0x0A
#define PCF8563_DAY_ALARM_REG 0x0B
#define PCF8563_WDAY_ALARM_REG 0x0C

/*CLKOUT control register*/
#define  PCF8563_CLKOUT_CON_REG 0x0D

LOCAL STATUS pcf8563RtcRead (UINT32 reg, UINT8 *buf);
LOCAL STATUS pcf8563RtcWrite  (UINT32 reg, UINT8 val);

LOCAL UINT8 BCD_TO_BIN (UINT8 n);
LOCAL UINT8 BIN_TO_BCD (UINT32 n);
/*******************************************************************************
*
* pcf8563RtcRead - read data from I2C
* 
* This routine read data from I2C.
*
* RETURNS: read data
*/
LOCAL STATUS pcf8563RtcRead (UINT32 reg, UINT8 *buf)
{
       if(buf == NULL)
        {
            return ERROR;
        }
	return (vxbI2cByNameRead(PCF8563_RTC_NAME,4,reg,buf,1));
}
/*******************************************************************************
*
* pcf8563RtcWrite - write data to I2C
* 
* This routine write data to I2C.
*
* RETURNS: N/A
*/
LOCAL STATUS pcf8563RtcWrite  (UINT32 reg, UINT8 val)
{
	return (vxbI2cByNameWrite(PCF8563_RTC_NAME,4,reg,&val,1));
}
/*
 * the following macros convert from BCD to binary and back.
 * Be careful that the arguments are chars, only char width returned.
 */
LOCAL UINT8 BCD_TO_BIN (UINT8 n)
{
	return ((((n >> 4) & 0x0F) * 10) + (n & 0x0F));
}

LOCAL UINT8 BIN_TO_BCD (UINT32 n)
{
	return (((n / 10) << 4) | (n % 10));
}
/*******************************************************************************
*
* pcf8563RtcGet - get current RTC date/time. 
*
* This routine allows the caller to obtain the current RTC time and date.
* The caller must allocate space for an RTC_DATE_TIME structure, then call
* this routine.  
*
* RETURNS: OK, or ERROR if unable to retrieve the current RTC date and time. 
*/
STATUS pcf8563RtcGet (RTC_DATE_TIME *rtc_time )
{
	int rel = OK;
	UINT8 sec=0, min=0, hour=0, mday=0, wday=0, mon_cent=0, year=0;
       if(rtc_time == NULL)
        {
              return ERROR;
        }
	if(pcf8563RtcRead (PCF8563_SEC_REG,&sec)==ERROR)/*get second*/
		return ERROR;
      if(pcf8563RtcRead (PCF8563_MIN_REG,&min)==ERROR)/*get minute*/
	  	return ERROR;
      if(pcf8563RtcRead (PCF8563_HOUR_REG,&hour)==ERROR)/*get hour*/
		return ERROR;
      if(pcf8563RtcRead (PCF8563_DAY_REG,&mday)==ERROR)/*get day*/
	  	return ERROR;
	 if(pcf8563RtcRead (PCF8563_WDAY_REG,&wday)==ERROR)/*get wday*/
		return ERROR;
      if(pcf8563RtcRead (PCF8563_MONTH_REG,&mon_cent)==ERROR)/*get mouth*/
	  	return ERROR;  
	if(pcf8563RtcRead (PCF8563_YEAR_REG,&year)==ERROR)/*get year*/
		return ERROR;  

        /* check Voltage Low bit */
	if (sec & 0x80) {
		printf ("### Warning: RTC Low Voltage - date/time not reliable\n");
		rel = ERROR;
	}

	rtc_time->tm_sec  = BCD_TO_BIN (sec  & 0x7F);
	rtc_time->tm_min  = BCD_TO_BIN (min  & 0x7F);
	rtc_time->tm_hour = BCD_TO_BIN (hour & 0x3F);
	rtc_time->tm_mday = BCD_TO_BIN (mday & 0x3F);
	rtc_time->tm_mon  = BCD_TO_BIN (mon_cent & 0x1F);
	rtc_time->tm_year = BCD_TO_BIN (year) + ((mon_cent & 0x80) ? 2000 : 1900);
	rtc_time->tm_wday = BCD_TO_BIN (wday & 0x07);

	PCF8563_RTC_DBG( "Get DATE: %4d-%02d-%02d (wday=%d)  TIME: %2d:%02d:%02d\n",
		rtc_time->tm_year, rtc_time->tm_mon, rtc_time->tm_mday, rtc_time->tm_wday,
		rtc_time->tm_hour, rtc_time->tm_min, rtc_time->tm_sec);

	return rel;
}

/*******************************************************************************
*
* pcf8563RtcSet - set the RTC's date/time per caller's values.
*
* This routine allows the caller to set the RTC time and date.  The caller
* must allocate space for an RTC_DATE_TIME structure, fill the structure
* with the desired time and date, and call this routine.
*
* RETURNS: OK, or ERROR if date/time values are invalid.
*/
STATUS  pcf8563RtcSet(RTC_DATE_TIME *rtc_time) 
{
	UINT8 century;

	PCF8563_RTC_DBG ( "Set DATE: %4d-%02d-%02d (wday=%d)  TIME: %2d:%02d:%02d\n",
		rtc_time->tm_year, rtc_time->tm_mon, rtc_time->tm_mday, rtc_time->tm_wday,
		rtc_time->tm_hour, rtc_time->tm_min, rtc_time->tm_sec);

	if(pcf8563RtcWrite (PCF8563_YEAR_REG, BIN_TO_BCD(rtc_time->tm_year % 100))==ERROR)
		return ERROR;

	century = (rtc_time->tm_year >= 2000) ? 0x80 : 0;
	if(pcf8563RtcWrite (PCF8563_MONTH_REG, BIN_TO_BCD(rtc_time->tm_mon) | century)==ERROR)
		return ERROR;

	if(pcf8563RtcWrite (PCF8563_WDAY_REG, BIN_TO_BCD(rtc_time->tm_wday))==ERROR)
		return ERROR;
	
	if(pcf8563RtcWrite (PCF8563_DAY_REG, BIN_TO_BCD(rtc_time->tm_mday))==ERROR)
		return ERROR;
	
	if(pcf8563RtcWrite (PCF8563_HOUR_REG, BIN_TO_BCD(rtc_time->tm_hour))==ERROR)
		return ERROR;
	
	if(pcf8563RtcWrite (PCF8563_MIN_REG, BIN_TO_BCD(rtc_time->tm_min ))==ERROR)
		return ERROR;
	
	if(pcf8563RtcWrite (PCF8563_SEC_REG, BIN_TO_BCD(rtc_time->tm_sec ))==ERROR)
		return ERROR;

	return OK;
}

/*******************************************************************************
*
* pcf8563RtcReset - reset RTC time
* 
*
* RETURNS:N/A
*/
STATUS pcf8563RtcReset (void)
{
       UINT8 tmp_value=0;
	/* clear all control & status registers */
	PCF8563_RTC_DBG("coming pcf8563ResetRtc:here1!\n");
	if(pcf8563RtcWrite (PCF8563_CON_STA1_REG, 0x00)==ERROR)
		return ERROR;
	
	PCF8563_RTC_DBG("coming pcf8563ResetRtc:here2!\n");
	if(pcf8563RtcWrite (PCF8563_CON_STA2_REG, 0x00)==ERROR)
		return ERROR;
	
	if(pcf8563RtcWrite (PCF8563_CLKOUT_CON_REG, 0x00)==ERROR)
		return ERROR;

	/* clear Voltage Low bit */
	if(pcf8563RtcRead (PCF8563_SEC_REG,&tmp_value)==ERROR)
		return ERROR;
	   
	if(pcf8563RtcWrite (PCF8563_SEC_REG,  tmp_value&0x7F)==ERROR)
		return ERROR;

	/* reset all alarms */
	if(pcf8563RtcWrite (PCF8563_MIN_ALARM_REG, 0x00)==ERROR)
		return ERROR;
	if(pcf8563RtcWrite (PCF8563_HOUR_ALARM_REG, 0x00)==ERROR)
		return ERROR;
	if(pcf8563RtcWrite (PCF8563_DAY_ALARM_REG, 0x00)==ERROR)
		return ERROR;
	if(pcf8563RtcWrite (PCF8563_WDAY_ALARM_REG, 0x00)==ERROR)
		return ERROR;

	return OK;
}

