# E2000D VxWorks 6.9 BSP (板级支持包)

## 项目概述

本项目是为飞腾(Phytium) E2000D处理器开发的VxWorks 6.9板级支持包(BSP)。E2000D是一款用于嵌入式应用的处理器，基于2颗FTC310核心。

### 处理器主要特性
- 基于ARM v8 64位指令系统，兼容32位指令
- 支持单精度、双精度浮点运算指令
- L1缓存32KB，L2缓存256KB
- 集成1个DDR4/LPDDR4-2400控制器，支持36bit地址
- 集成4 Lanes PCIE3.0接口（4X1）
- 集成4个1000M以太网控制器
- 集成3路USB2.0(OTG)和2路USB3.0（接口2.0）
- 集成1路I2S接口（支持高保真音频）和1路DisplayPort1.4接口
- 集成2路SATA3.0控制器
- 集成2个SD控制器
- 常用的低速接口：WDT，DMAC，QSPI，Nand，SPI_M，UART，I2C，CAN，GPIO，LocalBus，Timer等

## 文件结构说明

### 核心系统文件
- **config.h** - 系统配置头文件，定义了BSP版本、内存配置、设备选项等
- **sysLib.c** - 系统初始化和底层功能实现
- **hwconf.c** - 硬件配置文件，包含中断控制器、设备资源等配置
- **Makefile** - 编译系统构建规则
- **target.ref** - 目标板参考文档，包含编译环境、启动方法和硬件接口说明
- **defs.h** - 系统定义和常量
- **sysALib.s** - 汇编实现的系统功能

### 设备驱动程序
- **vxbArmGenIntCtlrV3.c/h** - ARM GICv3中断控制器驱动
- **vxbArmGenTimer.c** - ARM通用定时器驱动
- **vxbArmAuxTimer.c** - ARM辅助定时器驱动
- **vxbFtPcie.c** - 飞腾PCIe驱动
- **vxbFtGpio.c/h** - 飞腾GPIO驱动
- **ftPinMux.c/h** - 引脚复用配置
- **vxbFtQspi.c/h** - 飞腾QSPI控制器驱动
- **vxbFtSpi.c/h** - 飞腾SPI控制器驱动
- **vxbSp25SpiFlash.c/h** - SPI闪存驱动
- **vxbFtWdt.c** - 飞腾看门狗定时器驱动
- **vxbFtWdtLib.c** - 看门狗定时器库函数
- **vxbFtGpTimer.c/h, vxbFtGptLib.c/h** - 飞腾通用定时器驱动及库函数
- **vxbFtGemEnd.c/h** - 飞腾以太网驱动
- **vxbFtI2c.c/h** - 飞腾I2C控制器驱动
- **vxbFtKeypad.c/h** - 飞腾键盘驱动
- **vxbFtPwm.c/h, vxbPwmDrv.c/h** - 飞腾PWM驱动
- **vxbFtGDma.c/h** - 飞腾DMA控制器驱动
- **vxbFtSdCtrl.c/h** - 飞腾SD卡控制器驱动
- **vxbFtcan.c/h** - 飞腾CAN总线驱动
- **vxbM6845Vga.c** - VGA显示驱动
- **ftRtc.c** - 飞腾实时时钟驱动
- **vxbPrimeCellSioWithMIO.c** - PrimeCell串行IO驱动（带MIO功能）
- **vxbNgbeEnd.c/h** - 千兆以太网驱动

### 扩展设备驱动
- **ebtSpiRam.c/h** - SPI RAM驱动
- **vxbCy15bSpiRam.c/h** - Cypress SPI RAM驱动
- **ebtPcf8563Rtc.c/h** - PCF8563实时时钟芯片驱动
- **ebtNca9534VxDrv.c/h** - NCA9534芯片驱动
- **ebtFtLpc.c/h** - 低引脚数接口驱动
- **ebtEepromDrv.c/h** - EEPROM存储器驱动
- **ebtNST112VxDrv.c/h** - NST112芯片驱动
- **ebtSpiMCP2510Drv.c/h** - MCP2510 SPI CAN控制器驱动
- **ebtUART485VxDrv.c** - RS485串行接口驱动
- **ebtDualRamDrv.c/h** - 双端口RAM驱动
- **ebtMathVxDrv.c/h** - 数学库驱动

### 配置文件
- **20bsp.cdf** - BSP组件配置
- **40vxb***.cdf** - VxBus组件配置文件，为不同设备驱动配置参数

## 编译指南

### VxWorks编译环境

1. **代码源文件**
   - 将项目源代码放置在VxWorks 6.9开发环境(WIND_HOME安装目录)的相应位置
   - BSP的路径是：`<WIND_HOME>\vxworks-6.9\target\config\<BSP-source>`
   - 库的路径是：`<WIND_HOME>\vxworks-6.9\target\{src,h}\`
   - 如果只更新BSP，可以直接在Workbench开发环境中操作
   - 如果有源文件更新，请先编译库，再用Workbench构建

2. **命令行编译**
   支持两种编译模式：SMP（对称多处理）和UP（单处理器）
   
   在CMD命令窗口，设置开发环境：
   ```
   cd <WIND_HOME>
   wrenv.exe -p vxworks-6.9
   cd <WIND_HOME>\vxworks-6.9\target\src
   ```
   
   编译SMP库：
   ```
   make CPU=ARMARCH7 TOOL=diab VXBUILD=SMP
   ```
   编译完成后，检查`<WIND_HOME>\vxworks-6.9\target\lib_smp\`目录下的文件更新。
   
   编译UP库：
   ```
   make CPU=ARMARCH7 TOOL=diab
   ```
   编译完成后，检查`<WIND_HOME>\vxworks-6.9\target\lib\`目录下的文件更新。
   
   清理编译：
   ```
   make CPU=ARMARCH7 TOOL=diab rclean                 //删除并重新编译UP库
   make CPU=ARMARCH7 TOOL=diab VXBUILD=SMP rclean     //删除并重新编译SMP库
   ```

3. **图形化编译**
   推荐使用Workbench工具的VxWorks Source Build(Kernel Library) Project菜单创建VSB工程。
   只有VSB工程才能修改系统默认库，然后在基于此VSB的VxWorks Image Project（即VIP工程）中构建镜像。

## VxWorks启动指南

支持多种引导方式，如网络、硬盘、USB等方式。以下是常用的引导方法：

### 1. 通过TFTP网络加载

#### 1.1 PC上设置TFTP服务器
- Windows系统可直接使用Workbench目录下的TFTP服务器：
  `<workbench安装目录>\vxworks-6.9\host\x86-win32\bin\Tftpd32.exe`
- 设置Current Directory指向vxWorks镜像文件所在目录

#### 1.2 配置开发板U-Boot网络参数
通过串口终端（115200波特率）连接开发板，在U-Boot提示符下配置：
```
setenv ipaddr *************
setenv serverip *************
saveenv
```

#### 1.3 加载并运行操作系统镜像

bin格式加载：
```
tftpboot 0x80100000 vxWorks.bin
go 0x80100000
```

ELF格式加载：
```
tftpboot 0x90100000 vxWorks
bootelf 0x90100000
```

### 2. 通过文件系统加载

#### 2.1 USB盘加载
```
usb start
fatload usb 0 0x80100000 vxWorks.bin
go 0x80100000
```

#### 2.2 FAT32硬盘加载
```
fatload scsi 0:1 0x80100000 vxWorks.bin
go 0x80100000
```

#### 2.3 EXT4硬盘加载
```
ext4load scsi 0:1 0x80100000 vxWorks.bin
go 0x80100000
```

## 硬件接口

### 支持的硬件接口和控制器列表

| 硬件接口 | 控制器类型 | 驱动/组件 | 状态 | 组件定义 |
|----------|------------|-----------|------|---------|
| UART:0-3 | 片上集成 | vxbPrimeCellSio.c | 支持 | DRV_SIO_PRIMECELL |
| 以太网 | 片上集成 | vxbFtGemEnd.c | 支持 | DRV_VXBEND_FTGEM, INCLUDE_GENERICPHY |
| USB 3.0 | 片上集成 | USB STACK | 支持 | INCLUDE_USB_XHCI_HCD_INIT |
| USB Disk | 片上集成 | USB STACK | 支持 | INCLUDE_USB_GEN2_STORAGE_INIT |
| SDMMC | 片上集成 | vxbFtSdCtrl.c | 支持 | DRV_FTSD, INCLUDE_SD_BUS |
| SATA | 片上集成 | vxbAhciStorage.c | 支持 | INCLUDE_DRV_STORAGE_AHCI |
| TIMER | 片上集成 | vxbArmv7GenTimer.c | 支持 | DRV_ARM_GEN_SYS_TIMER |
| TIMER | 片上集成 | vxbArmv7AuxTimer.c | 支持 | DRV_ARM_GEN_AUX_TIMER |
| TIMER | 片上集成 | vxbFtGptLib.c | 支持 | DRV_TIMER_FT_GPT |
| PCIe | 片上集成 | vxbFtPcie.c | 支持 | DRV_PCIBUS_FT, INCLUDE_PCI_BUS |
| GPIO | 片上集成 | vxbFtGpio.c | 支持 | DRV_FTGPIO |
| I2C | 片上集成 | vxbFtI2c.c | 支持 | DRV_FTI2C, INCLUDE_I2C_BUS |
| SPI | 片上集成 | vxbFtSpi.c | 支持 | DRV_FTSPI, INCLUDE_SPI_BUS |
| RTC | DS1339 | vxbI2cRtc.c | 支持 | DRV_I2C_RTC, INCLUDE_TIMER_RTC |
| WDT | 片上集成 | vxbFtWdt.c | 支持 | DRV_FTWDT |
| QSPI | 片上集成 | vxbFtQspi.c | 支持 | DRV_FTQSPI |
| GDMA | 片上集成 | vxbFtGDma.c | 支持 | DRV_FTGDMA |
| PWM | 片上集成 | vxbFtPwm.c | 支持 | DRV_FTPWM |
| CAN/FD | 片上集成 | vxbFtCan.c | 支持 | DRV_FTCAN |

### 串口配置
四个PrimeCell UART串口默认配置为：
- 波特率：115200
- 数据位：8位
- 校验位：无
- 停止位：1位
- 流控制：无

在shell下可以列出四个串行设备文件：
```
-> devs
drv name
  1 /tyCo/0
  1 /tyCo/1
  1 /tyCo/2
  1 /tyCo/3
```

应用程序可以使用文件系统接口控制访问这些设备，使用ioctl()设置/获取串口波特率，read()/write()收发数据。
在本平台上，/tyCo/1作为系统的控制台，请勿随意修改配置。其余串口可供程序使用。 