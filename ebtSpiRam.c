/*******************************************************************************\
    SpiRam.c -- EmbedTec  driver  File for the FRAM write and read    
    File name: ebtSpiRam.c  
    
\********************************************************************************/
#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include <config.h>
#include "ebtSpiRam.h"


/*******************************************************
 * 
 * @brief SpiRam_info-- FRAM信息显示
 * 
 ******************************************************/
void SpiRam_info(void)
{
	VXB_DEVICE_ID pInst = NULL;
	pInst = vxbInstByNameFind(SPI_FRAM_NAME,0);
	/*- 判断设备是否查找成功 */
	if(pInst == NULL)
	{
		printf("the device find fail ! ! !\n");
	}
	else
		cy15bspiRamShow(pInst,1000);
}

/*****************************************************************
 * @brief SpiRam_read-- nvRAM读操
 * 
 * @param start FRAM读数据的偏移地址 取值范围：0~SPI_FRAM_SIZE(512KB)
 * @param len FRAM读数据的长度(字节) 取值范围：非0
 * @param dst 存放FRAM读出的数据的目的地址 取值范围：非NULL
 * 
 * @return nvRAM成功读取的字节数
 *  ERROR 失败
 ****************************************************************/
int SpiRam_read(FLASH_ADDR_T start,FLASH_SIZE_T len, UINT8 *dst)
{
	VXB_DEVICE_ID pInst = NULL;
	FLASH_CHIP_ID flash_chip_id = NULL;
	UINT8 *addr = dst;
	pInst = vxbInstByNameFind(SPI_FRAM_NAME,0);
	/*- 判断设备是否查找成功 */
	if(pInst == NULL)
	{		
		spiram_drv_dbg("vxbInstByNameFine error!\n");
		return ERROR;
	}
	flash_chip_id = vxbRamChipInfoGet(pInst,0);
	/*- 判断片选ID是否为空 */
	if(flash_chip_id == NULL)
	{
		spiram_drv_dbg("vxbFlashChipInfoGet error!\n");
		return ERROR;
	}
	return vxbRamRead(flash_chip_id,start,0,len,(UINT8 **)&addr,NULL);
}

/*********************************************************************
 * @brief SpiRam_write_nvol-- FRAM写操作
 * 
 * @param start FRAM写数据的偏移地址 取值范围：0~SPI_FRAM_SIZE(512KB)
 * @param len FRAM写数据的长度(字节) 取值范围：非0
 * @param src 写入FRAM数据的源地址 取值范围：非NULL
 * 
 * @return FRAM成功写入的字节数
 *  ERROR 失败
 ********************************************************************/
int SpiRam_write(FLASH_ADDR_T start,FLASH_SIZE_T len, UINT8 *src)
{
	VXB_DEVICE_ID pInst = NULL;
	FLASH_CHIP_ID flash_chip_id = NULL;
	UINT8 *addr = src;
	
	pInst = vxbInstByNameFind(SPI_FRAM_NAME,0);
	/*- 判断设备是否查找成功 */
	if(pInst == NULL)
	{
		spiram_drv_dbg("vxbInstByNameFind error!\n");
		return ERROR;
	}
	flash_chip_id = vxbRamChipInfoGet(pInst,0);
	/*- 判断片选ID是否为空 */
	if(flash_chip_id == NULL)
	{
		spiram_drv_dbg("vxbFlashChipInfoGet error!\n");
		return ERROR;
	}
	return vxbRamWrite(flash_chip_id,start,0,len,(UINT8 **)&addr,NULL);
}





/*******************************************************
 * 
 * @brief SpiRam_info_nvol-- FRAM信息显示-- non-volatile 非易失，掉电后数据可保存
 * 
 ******************************************************/
void SpiRam_info_nvol(void)
{
	VXB_DEVICE_ID pInst = NULL;
	pInst = vxbInstByNameFind(SPI_RAM_DEVICE_NAME,1);
	/*- 判断设备是否查找成功 */
	if(pInst == NULL)
	{
		printf("the device find fail ! ! !\n");
	}
	else
		cy15bspiRamShow(pInst,1000);
}

/*****************************************************************
 * @brief SpiRam_read_nvol-- nvRAM读操作-- non-volatile 非易失，掉电后数据可保存
 * 
 * @param start nvRAM读数据的偏移地址 取值范围：0~SPI_RAM_SIZE(64KB)
 * @param len nvRAM读数据的长度(字节) 取值范围：非0
 * @param dst 存放FRAM读出的数据的目的地址 取值范围：非NULL
 * 
 * @return nvRAM成功读取的字节数
 *  ERROR 失败
 ****************************************************************/
int SpiRam_read_nvol(FLASH_ADDR_T start,FLASH_SIZE_T len, UINT8 *dst)
{
	VXB_DEVICE_ID pInst = NULL;
	FLASH_CHIP_ID flash_chip_id = NULL;
	UINT8 *addr = dst;
	pInst = vxbInstByNameFind(SPI_RAM_DEVICE_NAME,1);
	/*- 判断设备是否查找成功 */
	if(pInst == NULL)
	{		
		spiram_drv_dbg("vxbInstByNameFine error!\n");
		return ERROR;
	}
	flash_chip_id = vxbRamChipInfoGet(pInst,0);
	/*- 判断片选ID是否为空 */
	if(flash_chip_id == NULL)
	{
		spiram_drv_dbg("vxbFlashChipInfoGet error!\n");
		return ERROR;
	}
	return vxbRamRead(flash_chip_id,start,0,len,(UINT8 **)&addr,NULL);
}

/*********************************************************************
 * @brief SpiRam_write_nvol-- nvRAM写操作-- non-volatile 非易失，掉电后数据可保存
 * 
 * @param start nvRAM写数据的偏移地址 取值范围：0~SPI_RAM_SIZE(64KB)
 * @param len nvRAM写数据的长度(字节) 取值范围：非0
 * @param src 写入FRAM数据的源地址 取值范围：非NULL
 * 
 * @return nvRAM成功写入的字节数
 *  ERROR 失败
 ********************************************************************/
int SpiRam_write_nvol(FLASH_ADDR_T start,FLASH_SIZE_T len, UINT8 *src)
{
	VXB_DEVICE_ID pInst = NULL;
	FLASH_CHIP_ID flash_chip_id = NULL;
	UINT8 *addr = src;
	
	pInst = vxbInstByNameFind(SPI_RAM_DEVICE_NAME,1);
	/*- 判断设备是否查找成功 */
	if(pInst == NULL)
	{
		spiram_drv_dbg("vxbInstByNameFind error!\n");
		return ERROR;
	}
	flash_chip_id = vxbRamChipInfoGet(pInst,0);
	/*- 判断片选ID是否为空 */
	if(flash_chip_id == NULL)
	{
		spiram_drv_dbg("vxbFlashChipInfoGet error!\n");
		return ERROR;
	}
	return vxbRamWrite(flash_chip_id,start,0,len,(UINT8 **)&addr,NULL);
}





/*******************************************************
 * 
 * @brief SpiRam_info_vol-- FRAM信息显示-- volatile 易失，掉电数据不保存
 * 
 ******************************************************/
void SpiRam_info_vol(void)
{
	VXB_DEVICE_ID pInst = NULL;
	pInst = vxbInstByNameFind(SPI_RAM_DEVICE_NAME,2);
	/*- 判断设备是否查找成功 */
	if(pInst == NULL)
	{
		printf("the device find fail ! ! !\n");
	}
	else
		cy15bspiRamShow(pInst,1000);
}

/*****************************************************************
 * @brief SpiRam_read_vol-- nvRAM读操作-- volatile 易失，掉电数据不保存
 * 
 * @param start nvRAM读数据的偏移地址 取值范围：0~SPI_RAM_SIZE(64KB)
 * @param len nvRAM读数据的长度(字节) 取值范围：非0
 * @param dst 存放FRAM读出的数据的目的地址 取值范围：非NULL
 * 
 * @return nvRAM成功读取的字节数
 *  ERROR 失败
 ****************************************************************/
int SpiRam_read_vol(FLASH_ADDR_T start,FLASH_SIZE_T len, UINT8 *dst)
{
	VXB_DEVICE_ID pInst = NULL;
	FLASH_CHIP_ID flash_chip_id = NULL;
	UINT8 *addr = dst;
	pInst = vxbInstByNameFind(SPI_RAM_DEVICE_NAME,2);
	/*- 判断设备是否查找成功 */
	if(pInst == NULL)
	{		
		spiram_drv_dbg("vxbInstByNameFine error!\n");
		return ERROR;
	}
	flash_chip_id = vxbRamChipInfoGet(pInst,0);
	/*- 判断片选ID是否为空 */
	if(flash_chip_id == NULL)
	{
		spiram_drv_dbg("vxbFlashChipInfoGet error!\n");
		return ERROR;
	}
	return vxbRamRead(flash_chip_id,start,0,len,(UINT8 **)&addr,NULL);
}

/*********************************************************************
 * @brief SpiRam_write_vol-- nvRAM写操作-- volatile 易失，掉电数据不保存
 * 
 * @param start nvRAM写数据的偏移地址 取值范围：0~SPI_RAM_SIZE(64KB)
 * @param len nvRAM写数据的长度(字节) 取值范围：非0
 * @param src 写入FRAM数据的源地址 取值范围：非NULL
 * 
 * @return nvRAM成功写入的字节数
 *  ERROR 失败
 ********************************************************************/
int SpiRam_write_vol(FLASH_ADDR_T start,FLASH_SIZE_T len, UINT8 *src)
{
	VXB_DEVICE_ID pInst = NULL;
	FLASH_CHIP_ID flash_chip_id = NULL;
	UINT8 *addr = src;
	
	pInst = vxbInstByNameFind(SPI_RAM_DEVICE_NAME,2);
	/*- 判断设备是否查找成功 */
	if(pInst == NULL)
	{
		spiram_drv_dbg("vxbInstByNameFind error!\n");
		return ERROR;
	}
	flash_chip_id = vxbRamChipInfoGet(pInst,0);
	/*- 判断片选ID是否为空 */
	if(flash_chip_id == NULL)
	{
		spiram_drv_dbg("vxbFlashChipInfoGet error!\n");
		return ERROR;
	}
	return vxbRamWrite(flash_chip_id,start,0,len,(UINT8 **)&addr,NULL);
}
