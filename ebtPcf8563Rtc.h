/*************************************************
  Copyright (C),  Embed-Te<PERSON> ,TianJin
  File name:      ebtPcf8563Rtc.h
  Description:     pcf8563 RTC Functions  
   History:        
  1. Date:
     Author:
     Modification: 
    
*************************************************/
/*
This file contains the configuration parameters and control functions for 
the pcf8563 Real-time clock 
*/

#ifndef _PCF8563_RTC_H
#define _PCF8563_RTC_H

#ifdef __cplusplus
    extern "C" {
#endif /* __cplusplus */

/*
 * The struct used to pass data from the generic interface code to
 * the hardware dependend low-level code ande vice versa. Identical
 * to struct rtc_time used by the vxworks kernel.
 *
 */

 typedef struct rtctime {
	int tm_sec; /* second */
	int tm_min; /* minute */
	int tm_hour;   /* hour */
	int tm_mday;/* day of month */
	int tm_mon;/* month */
	int tm_year;/* year */
	int tm_wday;/* day of week */
}RTC_DATE_TIME ;

STATUS  pcf8563RtcSet(RTC_DATE_TIME *rtc_time) ;
STATUS pcf8563RtcGet (RTC_DATE_TIME *rtc_time );
STATUS pcf8563RtcReset (void);
 
 #ifdef __cplusplus
    }
#endif /* __cplusplus */

#endif /* _PCF8563_RTC_H */

