/*************************************************
  Copyright (C),  Embed-Tec ,TianJin
  File name:      ebtDualRamDrv.h
  Author:  zhuyangyang     Version:  V1.0      Date: 2017.5.19
  Description:     Dual Ram function  
   History:        
  1. Date:
     Author:
     Modification: 
    
*************************************************/
#ifndef _DUALRAM_Drv_H
#define _DUALRAM_Drv_H

#ifdef __cplusplus
    extern "C" {
#endif /* __cplusplus */
#include "ebtBasisVxDrv.h"
#define DUALRAM_MAX_ADDR  0x6FFF
#define DUALRAM_MIN_ADDR  0x2000
    
typedef int  (*_Vx_USRFUNCPTR) (UINT16 signal, uint32_t usrdata);  

typedef struct {
    _Vx_USRFUNCPTR usr_func;
    uint32_t       usr_data;
}USR_PARAM;  
    
STATUS DUALRAM_Write(UINT16 addr, UINT8 data);
STATUS DUALRAM_Read(UINT16 addr, UINT8 *data);
STATUS LPC_IRQ_Connect( USR_PARAM  *UsrParam);
STATUS DUALRAM_SendIRQ(int vector);
void Serial_Add_Write(UINT8 data);
void Serial_Add_Read(UINT8 *data);
#ifdef __cplusplus
    }
#endif /* __cplusplus */

#endif /* _DUALRAM_Drv_H */

