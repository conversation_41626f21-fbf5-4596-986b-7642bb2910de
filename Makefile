
CPU     = ARMARCH7
TOOL    = gnu

EXTRA_DEFINE  = -DCPU_CORTEXA8 -DARMMMU=ARMMMU_CORTEXA8 \
                -DARMCACHE=ARMCACHE_CORTEXA8


TGT_DIR = $(WIND_BASE)/target

include $(TGT_DIR)/h/make/defs.bsp

## Only redefine make definitions below this point, or your definitions will
## be overwritten by the makefile stubs above.

TARGET_DIR  = e2000d
BOARD       = E2000D


RELEASE     += bootrom_uncmp.hex

MACH_EXTRA      += vxbArmGenIntCtlrV3.o  \
                   vxbArmGenTimer.o  vxbArmAuxTimer.o \
                   vxbFtPcie.o  vxbFtGpio.o ftPinMux.o \
                   vxbFtQspi.o vxbFtSpi.o vxbSp25SpiFlash.o\
                   vxbFtWdt.o vxbFtWdtLib.o \
                   vxbFtGpTimer.o vxbFtGptLib.o \
                   vxbFtGemEnd.o \
                   vxbFtI2c.o \
                   vxbFtKeypad.o \
                   vxbFtPwm.o vxbPwmDrv.o \
                   vxbFtGDma.o \
                   vxbFtSdCtrl.o \
                   vxbFtcan.o \
                   vxbM6845Vga.o \
                   ftRtc.o \
                   vxbPrimeCellSioWithMIO.o
				   
ifneq ($(findstring  bootrom,$(MAKECMDGOALS)),bootrom)
LIB_EXTRA = lib/libFtE2kdcdrv.a
LIB_EXTRA += lib/libFtScmidrv.a
endif			   

RAM_LOW_ADRS    = 80100000 # RAM text/data address
RAM_HIGH_ADRS   = 81000000 # RAM text/data address

VMA_START   = 0x$(ROM_TEXT_ADRS)

#EXTRA_INCLUDE += -I./include

#ADDED_CFLAGS += -DNEED_EARLY_MAP

include $(TGT_DIR)/h/make/rules.bsp


