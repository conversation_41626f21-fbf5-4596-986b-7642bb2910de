/* ebtFtLpc.h - LPC Controller hardware driver */

#ifndef __INCvxbFtLpch
#define __INCvxbFtLpch

/* includes */

#include <vxWorks.h>
#include <intLib.h>
#include <arch/arm/intArmLib.h>
#include <spinLockLib.h>
#include <vxBusLib.h>
#include <vxAtomicLib.h>

#ifdef __cplusplus
extern "C" {
#endif

#define LPC_VECTOR 119
#define LPC_BASE 0x20000000
#define LPC_REGBASE 0x7FFFF00
#define INT_APB_SPACE_CONF 0xCC
#define REG_LONG_TIMIEOUT 0xC8
#define INT_STATE 0xC4
#define CLR_INT 0xC0
#define CREG_LPCM_DIV 0x807E214
#define NU_SERIRQ_CONFIG 0xB8
#define CLK_LPC_RSTN_O 0x9C
#define FIRMWR_ID_CONF_STRT 0xB0
#define DMA_CHNNLNU_CONF 0xAC
#define INT_MASK 0xA8
#define START_CYCLE 0xA4
#define MEM_HIGHBIT_ADDR 0xA0

UINT8 lpc_read(uint16_t port);
void lpc_write(uint16_t port, uint8_t val);
void lpc_disreset();
void lpc_init();
void lpc_deinit();
void lpc_disableInt();
void lpc_clearInt(void);
void lpc_enableInt();
int lpc_intConnect(VOIDFUNCPTR routine, _Vx_usr_arg_t parameter);
void lpc_intDisConnect(VOIDFUNCPTR routine, _Vx_usr_arg_t parameter);
#ifdef __cplusplus
}
#endif

#endif /* __INC_ebtFtI2c_H */
