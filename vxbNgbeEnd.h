#ifndef __INCvxbNgbeEndh
#define __INCvxbNgbeEndh

#ifdef __cplusplus
extern "C" {
#endif

#ifdef _WRS_CONFIG_LP64
#define NGB_64        /* 64-bit physical address support */
#endif  /* _WRS_CONFIG_LP64 */


#if (_VX_CPU_FAMILY == _VX_I80X86) && \
    (defined(_WRS_CONFIG_LP64) || !defined(NGB_64))
#undef NGB_VXB_DMA_BUF
#else
#define NGB_VXB_DMA_BUF
#endif

void ngbRegister (void);

/* debug log output */
#define NGB_DEBUG
#ifdef NGB_DEBUG
#define DBG_LOG(fmt,a1,a2,a3,a4,a5,a6)          \
        logMsg(fmt,a1,a2,a3,a4,a5,a6)
#else
#define DBG_LOG(fmt,a1,a2,a3,a4,a5,a6)   {}
//#define DBG_LOG(fmt,a1,a2,a3,a4,a5,a6)   do {} while (0)
#endif

/*#ifndef BSP_VERSION*/

/************ txgbe_register.h ************/
/* Vendor ID */
#ifndef PCI_VENDOR_ID_TRUSTNETIC
#define PCI_VENDOR_ID_TRUSTNETIC                0x8088
#endif

/* Device IDs */
#define NGB_DEV_ID_EM_TEST                   0x0000
#define NGB_DEV_ID_EM_WX1860AL_W             0x0100
#define NGB_DEV_ID_EM_WX1860A2               0x0101
#define NGB_DEV_ID_EM_WX1860A2S              0x0102
#define NGB_DEV_ID_EM_WX1860A4               0x0103
#define NGB_DEV_ID_EM_WX1860A4S              0x0104
#define NGB_DEV_ID_EM_WX1860AL2              0x0105
#define NGB_DEV_ID_EM_WX1860AL2S             0x0106
#define NGB_DEV_ID_EM_WX1860AL4              0x0107
#define NGB_DEV_ID_EM_WX1860AL4S             0x0108

#define NGB_DEV_ID_EM_WX1860A1               0x010a
#define NGB_DEV_ID_EM_WX1860AL1              0x010b

/********* some driver information define *************/
#define NGB_MTU                              1500
#define NGB_JUMBO_MTU                        9000
#define NGB_CLSIZE                           1536
#define NGB_NAME                             "gei"
#define NGB_TIMEOUT                          10000

#define NGB_MAX_RX                           64
#define NGB_MAR_CNT                          128
#define NGB_MAXFRAG                          16

#define NGB_RX_DESC_CNT                      512
#define NGB_TX_DESC_CNT                      512

#define NGB_TX_CLEAN_THRESH                  32

#define NGB_DEVTYPE_COPPER                   1  /* devtype media support copper */
#define NGB_DEVTYPE_FIBER                    2  /* devtype media support fiber */
#define NGB_DEVTYPE_OTHER                    3  /* devtype media support fiber */
#define NGB_MIS_RST_ST                       0x10030

#define NGB_PCI_MASTER_DISABLE_TIMEOUT       800

#define NGB_PX_INTA                          0x110

/*******************************PHY Register Config*********************************/

/* internal phy control */
#define NGB_INTERNAL_PHY_PAGE_SELECT_OFFSET  31
#define NGB_INTERNAL_PHY_OFFSET_MAX          32
#define NGB_INTERNAL_PHY_ID                  0x000732

#define NGB_CFG_LAN_SPEED                    0x14440

#define NGB_INTPHY_INT_LSC                   0x0010
#define NGB_INTPHY_INT_PRAI                  0x0020
#define NGB_INTPHY_INT_ANC                   0x0008

/* internal phy reg_offset [0,31] */
#define NGB_PHY_CONFIG(reg_offset)          (0x14000 + ((reg_offset) * 4))

#define NGB_LINK_UP_TIME                     90

/* Link speed */
#define NGB_LINK_SPEED_UNKNOWN               0
#define NGB_LINK_SPEED_100_FULL              1
#define NGB_LINK_SPEED_1GB_FULL              2
#define NGB_LINK_SPEED_10_FULL               8
#define NGB_LINK_SPEED_AUTONEG              (NGB_LINK_SPEED_100_FULL | \
                                             NGB_LINK_SPEED_1GB_FULL | \
                                             NGB_LINK_SPEED_10_FULL)

/******************************* PSR Registers *******************************/
/* psr control */
#define NGB_PSR_CTL                          0x15000
#define NGB_PSR_VLAN_CTL                     0x15088
#define NGB_PSR_CTL_BAM                      0x00000400U
#define NGB_PSR_CTL_UPE                      0x00000200U
#define NGB_PSR_CTL_MPE                      0x00000100U
#define NGB_PSR_CTL_MFE                      0x00000080U
#define NGB_PSR_CTL_MO                       0x00000060U
#define NGB_PSR_MAX_SZ                       0x15020

/* RX address excact match filter registers */
#define NGB_PARH_AV                          0x80000000 /* Address valid */

#define NGB_PSR_UC_TBL(_i)                  (0x15400  + ((_i) * 4))

/* mcasst/ucast overflow tbl */
#define NGB_PSR_MC_TBL(_i)                  (0x15200  + ((_i) * 4))

#define NGB_PSR_VM_L2CTL(_i)                (0x15600 + ((_i) * 4))

/* mac switcher */
#define NGB_PSR_MAC_SWC_AD_L                 0x16200
#define NGB_PSR_MAC_SWC_AD_H                 0x16204
#define NGB_PSR_MAC_SWC_IDX                  0x16210

/* VMOLR bitmasks */
#define NGB_PSR_VM_L2CTL_UPE                 0x00000010U /* unicast promiscuous */
#define NGB_PSR_VM_L2CTL_TPE                 0x00000020U /* ETAG promiscuous */
#define NGB_PSR_VM_L2CTL_VACC                0x00000040U /* accept nomatched vlan */
#define NGB_PSR_VM_L2CTL_VPE                 0x00000080U /* vlan promiscuous mode */
#define NGB_PSR_VM_L2CTL_AUPE                0x00000100U /* accept untagged packets */
#define NGB_PSR_VM_L2CTL_ROMPE               0x00000200U /*accept packets in MTA tbl*/
#define NGB_PSR_VM_L2CTL_ROPE                0x00000400U /* accept packets in UC tbl*/
#define NGB_PSR_VM_L2CTL_BAM                 0x00000800U /* accept broadcast packets*/
#define NGB_PSR_VM_L2CTL_MPE                 0x00001000U /* multicast promiscuous */

/********************************* RSEC **************************************/
/* general rsec */
#define NGB_RSC_CTL                          0x17000
#define NGB_RSC_CTL_CRC_STRIP                0x00000004U
#define NGB_TSEC_CTL                         0x1D000

#define NGB_TSEC_BUF_AE                      0x1D00C

/**************** Global Registers ****************************/
/* chip control Registers */
#define NGB_MIS_RST                          0x1000C
#define NGB_MIS_PWR                          0x10000

#define NGB_MIS_RST_SW_RST                   0x00000001U

#define NGB_MIS_RST_LAN0_RST                 0x00000002U
#define NGB_MIS_RST_LAN1_RST                 0x00000004U
#define NGB_MIS_RST_LAN2_RST                 0x00000008U
#define NGB_MIS_RST_LAN3_RST                 0x00000010U

#define NGB_MIS_RST_ST_DEV_RST_ST_DONE       0x00000000U
#define NGB_MIS_RST_ST_DEV_RST_ST_REQ        0x00080000U
#define NGB_MIS_RST_ST_DEV_RST_ST_INPROGRESS 0x00100000U
#define NGB_MIS_ST                           0x10028

/***************************** RDB registers *********************************/
/* receive packet buffer */
#define NGB_RDB_PB_SZ(_i)                   (0x19020 + ((_i) * 4))
#define NGB_RDB_PB_CTL                       0x19000

/* Receive Config masks */
#define NGB_RDB_PB_CTL_RXEN                 (0x80000000) /* Enable Receiver */
#define NGB_RDB_PB_CTL_DISABLED              0x1

/************************************* ETH MAC *****************************/
#define NGB_MAC_TX_CFG                       0x11000
#define NGB_MAC_RX_CFG                       0x11004
#define NGB_MAC_PKT_FLT                      0x11008
#define NGB_MAC_WDG_TIMEOUT                  0x1100C
#define NGB_MAC_PKT_FLT_PR                  (0x1) /* promiscuous mode */
#define NGB_MAC_PKT_FLT_RA                  (0x80000000) /* receive all */
#define NGB_MAC_RX_FLOW_CTRL                 0x11090
#define NGB_MAC_TX_FLOW_CTRL                 0x11070

#define NGB_MAC_TX_CFG_TE                    0x00000001U
#define NGB_MAC_TX_CFG_SPEED_MASK            0x60000000U
#define NGB_MAC_TX_CFG_SPEED_10G             0x00000000U
#define NGB_MAC_TX_CFG_SPEED_1G              0x60000000U
#define NGB_MAC_RX_CFG_RE                    0x00000001U
#define NGB_MAC_RX_CFG_JE                    0x00000100U
#define NGB_MAC_RX_CFG_LM                    0x00000400U
#define NGB_MAC_RX_FLOW_CTRL_RFE             0x00000001U /* receive fc enable */

/* statistic */
#define NGB_RX_BC_FRAMES_GOOD_LOW            0x11918
#define NGB_TX_MC_FRAMES_GOOD_LOW            0x1182C
#define NGB_TX_BC_FRAMES_GOOD_LOW            0x11824

/********************************* BAR registers ***************************/
/* Interrupt Registers */
#define NGB_PX_MISC_IC                       0x100
#define NGB_PX_MISC_ICS                      0x104
#define NGB_PX_MISC_IEN                      0x108
#define NGB_PX_MISC_IVAR                     0x4FC
#define NGB_PX_GPIE                          0x118

#define NGB_PX_MISC_IVAR_DEFAULT             0x1
#define NGB_PX_MISC_IVAR_VALID               0x00000080UL

#define NGB_PX_ISB_ADDR_L                    0x160
#define NGB_PX_ISB_ADDR_H                    0x164
#define NGB_PX_IC(_i)                       (0x120 + (_i) * 4)
#define NGB_PX_ICS(_i)                      (0x130 + (_i) * 4)
#define NGB_PX_IMS(_i)                      (0x140 + (_i) * 4)
#define NGB_PX_IMC(_i)                      (0x150 + (_i) * 4)
#define NGB_PX_IVAR(_i)                     (0x500 + (_i) * 4)
#define NGB_PX_ITR(_i)                      (0x200 + (_i) * 4)
#define NGB_PX_TRANSACTION_PENDING           0x168
#define NGB_PX_INTA                          0x110

/*  Interrupt vector allocation registers */
#define NGB_PX_IVAR0                         0x500UL
#define NGB_PX_IVAR_RX0_DEFAULT              0x00
#define NGB_PX_IVAR_RX0_VALID                0x00000080UL	/**< RX queue 0 valid */
#define NGB_PX_IVAR_TX0_DEFAULT              0x00
#define NGB_PX_IVAR_TX0_VALID                0x00008000UL	/**< TX queue 0 valid */

#define NGB_PX_ITR0                          0x200UL
#define NGB_DEFAULT_ITR                      200
#define NGB_PX_ITR_CNT_WDIS                  0x80000000U


/******************** Interrupt register bitmasks *****************/
/* Extended Interrupt Enable Set */
#define NGB_PX_MISC_IEN_ETH_LKDN      0x00000100U
#define NGB_PX_MISC_IEN_DEV_RST       0x00000400U
#define NGB_PX_MISC_IEN_TIMESYNC      0x00000800U
#define NGB_PX_MISC_IEN_STALL         0x00001000U
#define NGB_PX_MISC_IEN_LINKSEC       0x00002000U
#define NGB_PX_MISC_IEN_RX_MISS       0x00004000U
#define NGB_PX_MISC_IEN_I2C           0x00010000U
#define NGB_PX_MISC_IEN_ETH_EVENT     0x00020000U
#define NGB_PX_MISC_IEN_IC_PHY        0x00040000U
#define NGB_PX_MISC_IEN_ETH_AN        0x00080000U
#define NGB_PX_MISC_IEN_INT_ERR       0x00100000U
#define NGB_PX_MISC_IEN_SPI           0x00200000U
#define NGB_PX_MISC_IEN_VF_MBOX       0x00800000U
#define NGB_PX_MISC_IEN_GPIO          0x04000000U
#define NGB_PX_MISC_IEN_PCIE_REQ_ERR  0x08000000U
#define NGB_PX_MISC_IEN_OVER_HEAT     0x10000000U
#define NGB_PX_MISC_IEN_PROBE_MATCH   0x20000000U
#define NGB_PX_MISC_IEN_MNG_HOST_MBOX 0x40000000U
#define NGB_PX_MISC_IEN_TIMER         0x80000000U

#define NGB_PX_MISC_IEN_MASK ( \
				NGB_PX_MISC_IEN_ETH_LKDN| \
				NGB_PX_MISC_IEN_IC_PHY | \
				NGB_PX_MISC_IEN_ETH_AN | \
				NGB_PX_MISC_IEN_GPIO)

/**************************** Transmit DMA registers **************************/
/* transmit global control */
#define NGB_TDM_CTL           0x18000
#define NGB_TDM_PB_THRE       0x18020

#define NGB_TDM_CTL_TE        0x1 /* Transmit Enable */

/* transmit DMA Registers */
#define NGB_PX_TR_BAL(_i)     (0x03000 + ((_i) * 0x40))
#define NGB_PX_TR_BAH(_i)     (0x03004 + ((_i) * 0x40))
#define NGB_PX_TR_WP(_i)      (0x03008 + ((_i) * 0x40))
#define NGB_PX_TR_RP(_i)      (0x0300C + ((_i) * 0x40))

#define NGB_PX_TR_CFG(_i)     (0x03010 + ((_i) * 0x40))
#define NGB_PX_TR_CFG_ENABLE          (1) /* Ena specific Tx Queue */
#define NGB_PX_TR_CFG_TR_SIZE_SHIFT   1 /* tx desc number per ring */
#define NGB_PX_TR_CFG_SWFLSH          (1 << 26) /* Tx Desc. wr-bk flushing */
#define NGB_PX_TR_CFG_WTHRESH_SHIFT   16 /* shift to WTHRESH bits */
#define NGB_PX_TR_CFG_THRE_SHIFT      8
#define NGB_PX_TR_CFG_TR_SZ           0x0000007EU

/* statistic */
#define NGB_TDM_SEC_DRP               0x18304
#define NGB_TDM_PKT_CNT               0x18308
#define NGB_TDM_OS2BMC_CNT            0x18314

/**************************** Receive DMA registers **************************/

#define NGB_PX_RR_CFG(_i)             (0x01010 + ((_i) * 0x40))

/* statistic */
#define NGB_RDM_DRP_PKT                0x12500
#define NGB_RDM_BMC2OS_CNT             0x12510

/* Receive DMA Registers */
#define NGB_PX_RR_BAL(_i)             (0x01000 + ((_i) * 0x40))
#define NGB_PX_RR_BAH(_i)             (0x01004 + ((_i) * 0x40))
#define NGB_PX_RR_RP(_i)              (0x0100C + ((_i) * 0x40))
#define NGB_PX_RR_WP(_i)              (0x01008 + ((_i) * 0x40))

/* PX_RR_CFG bit definitions */
#define NGB_PX_RR_CFG_RR_SIZE_SHIFT           1
#define NGB_PX_RR_CFG_BSIZEPKT_SHIFT          2 /* so many KBs */
#define NGB_PX_RR_CFG_BSIZEHDRSIZE_SHIFT      6 /* 64byte resolution (>> 6)*/
#define NGB_PX_RR_CFG_VLAN            0x80000000U
#define NGB_PX_RR_CFG_DROP_EN         0x40000000U

#define NGB_PX_RR_CFG_SPLIT_MODE      0x04000000U
#define NGB_PX_RR_CFG_RR_THER         0x00070000U
#define NGB_PX_RR_CFG_RR_THER_SHIFT   16
#define NGB_PX_RR_CFG_RR_HDR_SZ       0x0000F000U
#define NGB_PX_RR_CFG_RR_BUF_SZ       0x00000F00U
#define NGB_PX_RR_CFG_RR_SZ           0x0000007EU
#define NGB_PX_RR_CFG_RR_EN           0x00000001U

#define NGB_PX_RR_CFG_HDR_SIZE        256
#define NGB_PX_RR_CFG_BSIZE_DEFAULT   2048
#define NGB_PX_RR_CFG_BSIZE_10K       10240

/* statistic */
#define NGB_PX_MPRC(_i)               (0x1020 + ((_i) * 64))
#define NGB_PX_GPRC                   0x12504
#define NGB_PX_GPTC                   0x18308
#define NGB_PX_GORC_LSB               0x12508
#define NGB_PX_GORC_MSB               0x1250C
#define NGB_PX_GOTC_LSB               0x1830C
#define NGB_PX_GOTC_MSB               0x18310


/************************* Port Registers ************************************/
/* port cfg Registers */
#define NGB_CFG_PORT_CTL              0x14400
#define NGB_CFG_PORT_ST               0x14404

/* port cfg bit */
#define NGB_CFG_PORT_CTL_PFRSTD       0x00004000U /* Phy Function Reset Done */

#define NGB_CFG_PORT_CTL_DRV_LOAD     0x00000008U
/* Status Bit */
#define NGB_CFG_PORT_ST_LINK_UP       0x00000001U
#define NGB_CFG_PORT_ST_READ_LAN_ID   0x00000100U


/* Advanced RX descriptor format */

typedef union ngb_adv_rdesc
    {
    struct
        {
        volatile UINT32     ngb_addrlo; /* NSE */
        volatile UINT32     ngb_addrhi;
        volatile UINT32     ngb_hdrlo; /* DD */
        volatile UINT32     ngb_hdrhi;
        } read;
    struct
        {
        volatile UINT16     ngb_ptype_rsstype;
        volatile UINT16     ngb_sph_hdrlen;
        volatile UINT32     ngb_rss; /* RSS Hash */
        volatile UINT16     ngb_sts;
        volatile UINT16     ngb_err;
        volatile UINT16     ngb_pkt_buflen;
        volatile UINT16     ngb_vlan;
        } write;
    struct
        {
        volatile UINT32     ngb_pktinfo;
        volatile UINT32     ngb_rss;
        volatile UINT32     ngb_errsts;
        volatile UINT16     ngb_len;
        volatile UINT16     ngb_vlan;
        } write_rss;
    } NGB_ADV_RDESC;

#define NGB_ADV_RDESC_READ_DD           0x00000001
#define NGB_ADV_RDESC_RTYPE_NONE        0x00000000	/* No hash */
#define NGB_ADV_RDESC_RTYPE_TCPIPV4     0x00000001
#define NGB_ADV_RDESC_RTYPE_IPV4        0x00000002
#define NGB_ADV_RDESC_RTYPE_TCPIPV6     0x00000003
#define NGB_ADV_RDESC_RTYPE_SCTP4       0x00000004  /* RSVD */
#define NGB_ADV_RDESC_RTYPE_IPV6        0x00000005
#define NGB_ADV_RDESC_RTYPE_SCTP6       0x00000006  /* RSVD */
#define NGB_ADV_RDESC_RTYPE_UDPIPV4     0x00000007
#define NGB_ADV_RDESC_RTYPE_UDPIPV6     0x00000008
#define NGB_ADV_RDESC_RTYPE_FDFS        0x0000000f
#define NGB_ADV_RDESC_PTYPE_RSVD        0x10000
#define NGB_ADV_RDESC_PTYPE_L2PKT       0x8000 /* When set, interpretation of other PTYPE bits changes */

#define NGB_RXD_ERR_RXE                 0x2000U /* Any MAC Error */
#define NGB_ADV_RDESC_ERR_TCPE          0x4000U /* TCP/UDP Checksum Error */

#define NGB_ADV_RDESC_STS_VP            0x0020U /* IEEE VLAN Pkt */
#define NGB_ADV_RDESC_STS_UDPCV         0x0040U /* UDP csum calculated */
#define NGB_ADV_RDESC_STS_L4CS          0x0080U /* L4 csum calculated */
#define NGB_ADV_RDESC_STS_IPCS          0x0100U /* IP xsum calculated */
#define NGB_ADV_RDESC_STS_PIF           0x0200U /* passed in-exact filter */
#define NGB_ADV_RDESC_STS_EIPCS         0x0400U /* Cloud IP xsum calculated*/
#define NGB_ADV_RDESC_STS_VEXT          0x0800U /* 1st VLAN found */
#define NGB_ADV_RDESC_STS_LLINT         0x2000U /* Pkt caused Low Latency */
#define NGB_ADV_RDESC_STS_DD            0x0001 /* Descriptor done */
#define NGB_ADV_RDESC_STS_EOP           0x0002 /* End of packet */

/* Extended Error and Extended Status considered as a single 32-bit field */
#define NGB_ADV_RDESC_ERRSTS_IPE        0x80000000 /* IPv4 checksum error */
#define NGB_ADV_RDESC_ERRSTS_TCPE       0x40000000 /* TCP/UDP checksum error */
#define NGB_ADV_RDESC_ERRSTS_USE        0x20000000 /* Undersize (runt) frame */
#define NGB_ADV_RDESC_ERRSTS_OSE        0x10000000 /* Oversize (giant) frame */
#define NGB_ADV_RDESC_ERRSTS_PE         0x08000000 /* Packet symbol error */
#define NGB_ADV_RDESC_ERRSTS_LE         0x02000000 /* Length error */
#define NGB_ADV_RDESC_ERRSTS_CE         0x01000000 /* CRC error */
#define NGB_ADV_RDESC_ERRSTS_HBO        0x00800000 /* Header buffer overflow */
#define NGB_ADV_RDESC_ERRSTS_LB         0x00040000 /* Loopback - VM to VM */
#define NGB_ADV_RDESC_ERRSTS_SECP       0x00020000 /* Security encap. processed */
#define NGB_ADV_RDESC_ERRSTS_TS         0x00010000 /* Time sync packet */
#define NGB_ADV_RDESC_ERRSTS_DYNINT     0x00000800 /* Did low-latency interrupt */
#define NGB_ADV_RDESC_ERRSTS_UDPV       0x00000400 /* UDP csum valid */
#define NGB_ADV_RDESC_ERRSTS_VEXT       0x00000200 /* Outer VLAN found */
#define NGB_ADV_RDESC_ERRSTS_CRCV       0x00000100 /* Speculative CRC OK */
#define NGB_ADV_RDESC_ERRSTS_PIF        0x00000080 /* Passed inexact filter */
#define NGB_ADV_RDESC_ERRSTS_IPCS       0x00000040 /* IP csum calculated */
#define NGB_ADV_RDESC_ERRSTS_L4CS       0x00000020 /* TCP csum calculated */
#define NGB_ADV_RDESC_ERRSTS_UDPCS      0x00000010 /* UDP checksum calculated */
#define NGB_ADV_RDESC_ERRSTS_VLAN       0x00000008 /* 802.1q packet, stripped */
#define NGB_ADV_RDESC_ERRSTS_FLM        0x00000004 /* Flow director match */
#define NGB_ADV_RDESC_ERRSTS_EOP        0x00000002 /* End of packet */
#define NGB_ADV_RDESC_ERRSTS_DD         0x00000001 /* Descriptor done */

/* Advanced TCP/IP context descriptor format */
typedef struct ngb_adv_cdesc
    {
    volatile UINT16     ngb_macip;
    volatile UINT16     ngb_vlan;
    volatile UINT32     ngb_rsvd;
    volatile UINT32     ngb_cmd;
    volatile UINT8      ngb_idx;
    volatile UINT8      ngb_l4len;
    volatile UINT16     ngb_mss;
    } NGB_ADV_CDESC;

#define NGB_ADV_CDESC_MACIP_IP      0x01FF          /* IP header length */
#define NGB_ADV_CDESC_MACIP_MAC     0xFE00          /* Frame header length */
#define NGB_ADV_IPLEN(x)           ((x) & NGB_ADV_CDESC_MACIP_IP)
#define NGB_ADV_MACLEN(x)          (((x) << 9) & NGB_ADV_CDESC_MACIP_MAC)

typedef struct ngb_adv_tdesc
    {
    volatile UINT32     ngb_addrlo;
    volatile UINT32     ngb_addrhi;
    volatile UINT32     ngb_cmd;
    volatile UINT32     ngb_sts;
    } NGB_ADV_TDESC;

#define NGB_ADV_STS_DD                     0x00000001
#define NGB_DESC_STATUS_PAYLEN( len )     ( (len) << 13 )

#define NGB_ADV_TDESC_DTYP_CTX             0x00100000U /* Adv Context Desc */
#define NGB_ADV_TDESC_DTYP_DSC             0x00000000U /* Adv Data Descriptor */
#define NGB_ADV_TDESC_CMD_EOP              0x01000000U  /* End of Packet */
#define NGB_ADV_TDESC_CMD_IFCS             0x02000000U /* Insert FCS */
#define NGB_ADV_TDESC_CMD_LINKSEC          0x04000000U /* enable linksec */
#define NGB_ADV_TDESC_CMD_RS               0x08000000U /* Report Status */
#define NGB_ADV_TDESC_CMD_VLE              0x40000000U /* VLAN tag insertion Enable */
#define NGB_ADV_TDESC_L4CS                 0x00000200U

#define NGB_INC_DESC(x, y)                (x) = ((x + 1) & (y - 1))
#define NGB_ADDR_LO(y)                   ((UINT64)((UINT32) (y)) & 0xFFFFFFFF)
#define NGB_ADDR_HI(y)                  (((UINT64)((UINT32) (y)) >> 32) & 0xFFFFFFFF)
#define NGB_ADJ(m)                        (m)->m_data += 2

enum ngb_isb_idx
    {
    NGB_ISB_HEADER,
    NGB_ISB_MISC,
    NGB_ISB_VEC0,
    NGB_ISB_VEC1,
    NGB_ISB_MAX
    };

/*phy reg define*/

#define NGB_MIS_ST_GPHY_IN_RST(_r)          (0x00000200U << (_r))

/* Link speed */
#define NGB_LINK_SPEED_UNKNOWN               0
#define NGB_LINK_SPEED_100_FULL              1
#define NGB_LINK_SPEED_1GB_FULL              2
#define NGB_LINK_SPEED_10_FULL               8
#define NGB_LINK_SPEED_AUTONEG              (NGB_LINK_SPEED_100_FULL | \
                                             NGB_LINK_SPEED_1GB_FULL | \
                                             NGB_LINK_SPEED_10_FULL)


#define NGB_PHY_SPEED_SELECT1                0x0040
#define NGB_PHY_DUPLEX                       0x0100
#define NGB_PHY_RESTART_AN                   0x0200
#define NGB_PHY_ANE                          0x1000
#define NGB_PHY_SPEED_SELECT0                0x2000
#define NGB_PHY_RESET                        0x8000


/* Physical layer type */

#define NGB_PHYSICAL_LAYER_UNKNOWN           0
#define NGB_PHYSICAL_LAYER_1000BASE_T        0x0002
#define NGB_PHYSICAL_LAYER_100BASE_TX        0x0004
#define NGB_PHYSICAL_LAYER_SFP_PLUS_CU       0x0008
#define NGB_PHYSICAL_LAYER_1000BASE_KX       0x0200
#define NGB_PHYSICAL_LAYER_1000BASE_BX       0x0400
#define NGB_PHYSICAL_LAYER_SFP_ACTIVE_DA     0x2000
#define NGB_PHYSICAL_LAYER_1000BASE_SX       0x4000

enum ngb_phy_type
    {
    ngb_phy_unknown = 0,
    ngb_phy_none,
    ngb_phy_internal,
    ngb_phy_m88e1512,
    ngb_phy_zte,
    ngb_phy_sfp_passive_tyco,
    ngb_phy_sfp_passive_unknown,
    ngb_phy_sfp_active_unknown,
    ngb_phy_sfp_avago,
    ngb_phy_sfp_ftl,
    ngb_phy_sfp_ftl_active,
    ngb_phy_sfp_unknown,
    ngb_phy_sfp_intel,
    ngb_phy_sfp_unsupported, /*Enforce bit set with unsupported module*/
    ngb_phy_generic
    };

enum ngb_media_type
    {
    ngb_media_type_unknown = 0,
    ngb_media_type_fiber,
    ngb_media_type_copper,
    ngb_media_type_backplane,
    ngb_media_type_virtual
    };

/* PHY MDI STANDARD CONFIG */
#define NGB_MDI_PHY_ID1_OFFSET                2
#define NGB_MDI_PHY_ID2_OFFSET                3
#define NGB_MDI_PHY_ID_MASK                   0xFFFFFC00U
#define NGB_MDI_PHY_SPEED_SELECT1             0x0040
#define NGB_MDI_PHY_DUPLEX                    0x0100
#define NGB_MDI_PHY_RESTART_AN                0x0200
#define NGB_MDI_PHY_ANE                       0x1000
#define NGB_MDI_PHY_SPEED_SELECT0             0x2000
#define NGB_MDI_PHY_RESET                     0x8000


#define NGB_MSCA                              0x11200
#define NGB_MSCA_RA(v)                      ((0xFFFF & (v)))
#define NGB_MSCA_PA(v)                      ((0x1F & (v)) << 16)
#define NGB_MSCA_DA(v)                      ((0x1F & (v)) << 21)
#define NGB_MSCC                              0x11204
#define NGB_MSCC_DATA(v)                    ((0xFFFF & (v)))
#define NGB_MSCC_CMD(v)                     ((0x3 & (v)) << 16)

#define NGB_MDIO_CLAUSE22                     0x11220

/*
 * Private adapter context structure.
 */

typedef struct ngb_drv_ctrl
    {
    END_OBJ              ngbEndObj;
    VXB_DEVICE_ID        ngbDev;
    void *               ngbBar;
    void *               ngbHandle;
    void *               ngbMuxDevCookie;

    JOB_QUEUE_ID         ngbJobQueue;
    QJOB                 ngbIntJob;
    atomic_t             ngbIntPending;
    UINT32               ngbIntrs;
    UINT32               ngbMoreRx;

    BOOL                 ngbPolling;
    M_BLK_ID             ngbPollBuf;
    UINT32               ngbIntMask;

    UINT8                ngbAddr[ETHER_ADDR_LEN];

    END_CAPABILITIES     ngbCaps;

    END_IFDRVCONF        ngbEndStatsConf;
    END_IFCOUNTERS       ngbEndStatsCounters;

    SEM_ID               ngbDevSem;

    /* Begin MII/ifmedia required fields. */
    END_MEDIALIST       *ngbMediaList;
    END_ERR              ngbLastError;
    UINT32               ngbCurMedia;
    UINT32               ngbCurStatus;
    /* End MII/ifmedia required fields */

#ifdef NGB_VXB_DMA_BUF
    /* DMA tags and maps. */
    VXB_DMA_TAG_ID       ngbParentTag;

    VXB_DMA_TAG_ID       ngbRxDescTag;
    VXB_DMA_TAG_ID       ngbTxDescTag;
    VXB_DMA_TAG_ID       ngbIsbTag;

    VXB_DMA_MAP_ID       ngbRxDescMap;
    VXB_DMA_MAP_ID       ngbTxDescMap;
    VXB_DMA_MAP_ID       ngbIsbMap;

    VXB_DMA_TAG_ID       ngbMblkTag;

    VXB_DMA_MAP_ID       ngbRxMblkMap[NGB_RX_DESC_CNT];
    VXB_DMA_MAP_ID       ngbTxMblkMap[NGB_TX_DESC_CNT];
#else
    /* possibly unaligned desc buffer to free */
    char *               ngbDescBuf;
    char *               ngbIsbBuf;
#endif

    NGB_ADV_RDESC *      ngbRxDescMem;
    NGB_ADV_TDESC *      ngbTxDescMem;
    UINT32 *             isb_mem;

    M_BLK_ID             ngbTxMblk[NGB_TX_DESC_CNT];
    M_BLK_ID             ngbRxMblk[NGB_RX_DESC_CNT];

    UINT32               ngbTxProd;
    UINT32               ngbTxCons;
    UINT32               ngbTxFree;
    BOOL                 ngbTxStall;

    UINT32               ngbRxIdx;

    UINT32               ngbLastCtx;
#ifdef CSUM_IPHDR_OFFSET
    int                  ngbLastOffsets;
#else
    int                  ngbLastIpLen;
#endif
    UINT16               ngbLastVlan;

    int                  ngbMaxMtu;
    UINT16               ngbDevId;
    int                  ngbDevType;
    enum ngb_phy_type    phy_type;
    } NGB_DRV_CTRL;


#if (CPU_FAMILY == I80X86)

#define CSR_READ_4(pDev, addr)                                  \
                *(volatile UINT32 *)((UINT32)pDev->pRegBase[0] + addr)

#define CSR_WRITE_4(pDev, addr, data)                           \
             do {                                                    \
                 volatile UINT32 *pReg =                             \
                     (UINT32 *)((UINT32)pDev->pRegBase[0] + addr);   \
                 *(pReg) = (UINT32)(data);                           \
             } while ((0))

#else /* CPU_FAMILY == I80X86 */

#define NGB_BAR(p)   ((NGB_DRV_CTRL *)(p)->pDrvCtrl)->ngbBar
#define NGB_HANDLE(p)   ((NGB_DRV_CTRL *)(p)->pDrvCtrl)->ngbHandle

#define CSR_READ_4(pDev, addr)                                  \
         vxbRead32 (NGB_HANDLE(pDev), (UINT32 *)((char *)NGB_BAR(pDev) + addr))

#define CSR_WRITE_4(pDev, addr, data)                           \
         vxbWrite32 (NGB_HANDLE(pDev),                          \
             (UINT32 *)((char *)NGB_BAR(pDev) + addr), data)

#endif /* CPU_FAMILY == I80X86 */

#define CSR_SETBIT_4(pDev, offset, val)          \
             CSR_WRITE_4(pDev, offset, CSR_READ_4(pDev, offset) | (val))

#define CSR_CLRBIT_4(pDev, offset, val)          \
             CSR_WRITE_4(pDev, offset, CSR_READ_4(pDev, offset) & ~(val))

/*#endif*/ /* BSP_VERSION */

#ifdef __cplusplus
}
#endif

#endif /* __INCvxbNgbeEndh */
