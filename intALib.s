/* intALib.s - interrupt library assembly language routines */

/*
 * Copyright (c) 1996-1998, 2000-2013,2015 Wind River Systems, Inc.
 *
 * The right to copy, distribute, modify, or otherwise make use
 * of this software may be licensed only pursuant to the terms
 * of an applicable Wind River license agreement.
 */

/*
modification history
--------------------
03f,30jun15,rlp  Removed symbol dependencies between VxDBG CPU library and
                 architecture code (VXW6-84541).
03e,20feb13,m_h  CortexA15 GuestOS support through VSB configuration
03d,15may13,cfm  Clear the thumb bit in PC. (WIND00417245)
03c,24oct12,jdw  Remove _fpStatus references reintroduced by merge,
	         WIND00384180
03b,16oct12,jdw  Add SPSR set macro (WIND00259770)
03a,18jan12,rec  WIND00329076 - fix stack backtrace.  General cleanup
02z,02dec11,jdw  Remove _fpStatus, WIND00129622
02y,23oct11,sem  WIND00312771 - revise power mgmt hook usage
02x,28sep11,rec  WIND00297108 - remove intExit power mgmt hook
02w,17aug11,rec  WIND00293391 - rework fix r12 corruption
02v,15aug11,rec  WIND00293391 - fix power management callbacks
02u,09aug11,rec  WIND00255352 - Fix preprocessor error
02t,04aug11,rec  WIND00255352 - Power management VSB build
02s,01feb11,jdw  Change writes to SPSR to mask fields (WIND00253307)
02r,23nov10,j_b  add unified FIQ/IRQ handling support
02q,29sep10,rab  Guest OS Support
02p,21may10,m_h  Thumb-2 Support
02o,10feb10,rlp  Added vxdbgLibP header file
02n,18may09,j_b  add intCpuMicroLock/Unlock
02n,25feb09,j_b  merge ARM SMP support:
                 21feb08,j_b  final intLock/Unlock func naming for UP and SMP
                 30jan08,scm  correct check-in, unlock interrupts before
                 workQDoWork, reorder intExit
                 12nov07,jmp  add intVxdbgCpuRegsGet() routine to allow VxDBG
                 CPU lib to save the register set of the interrupted context
   when CPU control is enabled in SMP mode
02m,03sep08,jpb  Renamed VSB config file.
02l,19may08,tcr  update SV instrumentation
02k,18jun08,jpb  Added include path for kernel configurations options set in
                 vsb.
02j,24mar08,m_h  Support for ARM 7tdmi (MMUless)
02i,21may08,jpb  Modifications for source build.  Renamed WV_INSTRUMENTATION
                 to _WRS_CONFIG_SV_INSTRUMENTATION, _WRS_VX_SMP to
                 _WRS_CONFIG_SMP.
02h,12jun07,m_h  Optimize intExit_RTI, TCB before saveIntContext
02g,11jun07,j_b  remove single, global errno handling
02f,16may07,lei  renamed reschedNeeded to reschedMode.
02e,09may07,m_h  per-CPU Value Set Issues; SMP bug fixes
02d,07may07,m_h  convert vxIntStackBase to a per-CPU global
02c,12apr07,m_h  SMP Globals and scheduler mods
02b,21dec06,h_k  added intCpuLock/intCpuUnlock.
02a,08dec05,jb   Fix for SPR 113327 - Load sp from TCB
01z,11jul05,h_k  cleaned up mmuArchTtbrGet.
01y,22mar05,scm  handle interrupt stack protection
01x,25feb05,scm  correct interrupts...
01w,17feb05,scm  move to exception stack, and add USER MODE support...
01v,01oct04,scm  hold off on switch to exception stack...
01u,07sep04,scm  move to exception stack adjust excCnt...
01t,26aug03,rec  remove SYS_PWR_MGMT conditional
01s,11jun03,pcm  make <kernelState> FALSE when idle
01r,28may03,rec  Power Management
01q,09apr03,ymz  do not assume qNode is the first element in WIND_TCB
01p,16jan02,to   add context switch for _fpStatus
01o,17oct01,t_m  convert to FUNC_LABEL:
01n,11oct01,jb   Enabling removal of pre-pended underscores for new compilers
                 (Diab/Gnu elf)
01n,09nov01,scm  remove IF_bitTest...
01m,23jul01,scm  change XScale name to conform to coding standards...
01l,15feb01,jb   Isolate FUNC(IF_bitTest) to XScale
01k,11dec00,scm  replace references to ARMSA2 with XScale
01j,03oct00,scm  add debug for sa2...
01i,13nov98,cdp  added back intLock(), intLock(); removed armIrqFlagSet();
   added intIFLock(), intIFUnlock(); tidied; optimise for no
   instrumentation plus minor other optimisations.
01h,02sep98,cjtc port to windView 2.0
01g,31jul98,pr   temporarily commentig out WindView code
01e,09oct97,cdp  change WindView code in intEnt so it logs nothing but
   just saves the timestamp; apply WindView fixes from
   Paola Rossaro (incorrect extern and label); tidy up.
01d,23sep97,cdp  removed kludges for old Thumb tool-chains.
01c,05aug97,cdp  rewritten for new interrupt structure.
   added WindView support.
01b,26mar97,cdp  added ARM7TDMI_T support.
01a,09may96,cdp  written.
*/

/*
DESCRIPTION
This library supports various functions associated with interrupts from
C routines. Note that VxWorks handles IRQ only: FIQ is left for code
outside VxWorks e.g. DRAM refresh, pseudo-DMA etc.

For SMP, the functions intCpuLock/intCpuUnlock are introduced
to lock/unlock interrupts on the local processor only. On Uniprocessor
systems, intCpuLock/intCpuUnlock and intLock/intUnlock behave identically -
in fact, they are the same routine. On SMP, intLock/intUnlock become
generic.

SEE ALSO: intLib, intArchLib

INTERNAL
The 68K versions of routines in this module "link" and "unlk" the "c"
frame pointer for the benefit of the stacktrace facility to allow it to
properly trace tasks executing within these routines. The ARM versions,
like the i86 versions, do not do the equivalent.

    +--------------------+      _intExit
    | intExit            |         |
    | state transition   |         +--> intExit_RTI -> rte
    | diagram            |         |
    +--------------------+         v
                             saveIntContext
                                   |
                                   v
                               reschedule
                                   |
                                   v
                           _windLoadContext
                                   |
                                   v
                                  rte (to new context)

General Uni-Processor Flow:

Flow to exit interrupt service routine:

            intExit()
               |
     __________|____________
    /                       \
    | in Kernel             | not in Kernel and
    v or Nested             v not Nested
    |                       |
intExit_RTI                 |
                            |
                            |
       Kernel is Idle       |
        --------------------|
       /                    |
       |                    v Kernel not Idle
   reschedule()             |
       |                    |
 windLoadContext()          |
              ______________|__________________
             /                                 \
             | current task =                  | current task not
             v highest ready task              v highest ready task
             |                _________________|______________
        intExit_RTI          /                                \
                             | preempt disabled               |
                             v or task not ready         saveIntContext
                             |                                |
                        intExit_RTI                       reschedule()
                                                              |
                                                        windLoadContext()
Current SMP version:

Flow to exit interrupt service routine:

                    intExit()
                       |
        lock local interrupts
             __________|____________
            /                       \
            |                       |
            | kernel lock taken     | kernel lock not taken by myself
            | by myself             | AND not Nested interrupt
            v OR Nested             v
            |                       |
       intExit_RTI                  |
            |                       |
    (decrement int counter &        |
     restore regs          &        |
     rfi)                           |
                               (fast path?)
                                    |
                       !reschedMode |   reschedMode
                      ______________|__________________
                     /                                 \
                     |                                 | no fast path
                     v < check workQ >                 v
               ______|_____                            |
  workQ empty /            \ not empty                 |
              |            |                 |->  saveIntContext
         intExit_RTI       |                 |         |
                           |                 |  switch stacks if necessary
                 unlock local interrupts     ^         |
                           |                 |  unlock local interrupts
                    take kernel lock         |         |
                           |                 |  take kernel lock
                 lock local interrupts       |         |
                           |                 |  lock local interrupts
                     emptyWorkQueue          |         |
                           |                 |  zero out intCnt &
                   give kernel lock          |          intNestingLevel
                           |                 ^         |
                      (fast path?)           |   reschedule()
                           |                 |         |
             !reschedMode  | reschedMode     |         |
                       ____|____             |  windLoadContext()
                      /         \            |
                      |         |            |
                 intExit_RTI    -------->-----

*/

#define _ASMLANGUAGE
#include <vxWorks.h>
#include <vsbConfig.h>
#include <arch/arm/asmArm.h>
#include <private/taskLibP.h>
#include <private/eventP.h>
#include <private/windLibP.h>
#include <private/vxSmpP.h>
#include <private/vxdbgLibP.h>

#ifdef _WRS_CONFIG_SMP
#define _VXDBG_PER_CPU_PREGS_GET(r, scratch)    \
 _ARM_CPU_INDEX_GET(r);      \
 LDR scratch, L$_vxdbgCpuCtrlVars;    \
 ADD r, scratch, r, LSL ARM_VXDBG_CPU_CTRL_VARS_ALIGN_SHIFT;
#endif /* _WRS_CONFIG_SMP */

 /* globals */

	FUNC_EXPORT(intCpuLock)	/* Local Interrupt Lock routine */
	FUNC_EXPORT(intCpuUnlock)	/* Local Interrupt Unlock routine */

	FUNC_EXPORT(intCpuMicroLock)	/* Local Interrupt uLock routine */
	FUNC_EXPORT(intCpuMicroUnlock)	/* Local Interrupt uUnlock routine */

#ifndef _WRS_CONFIG_SMP
	DATA_EXPORT(intNestingLevel)
#  ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ
	DATA_EXPORT(fiqNestingLevel)
#  endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */
	FUNC_EXPORT(intLock)
	FUNC_EXPORT(intUnlock)
#endif /* !_WRS_CONFIG_SMP */
#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ
	FUNC_EXPORT(intILock)
	FUNC_EXPORT(intIUnlock)
	FUNC_EXPORT(intFLock)
	FUNC_EXPORT(intFUnlock)
#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */
	FUNC_EXPORT(intIFLock)
	FUNC_EXPORT(intIFUnlock)
	FUNC_EXPORT(intVBRSet)
	FUNC_EXPORT(intEnt)		/* interrupt entry routine */
	FUNC_EXPORT(intExit)		/* interrupt exit routine */

#ifdef _WRS_CONFIG_SMP
	FUNC_EXPORT(intVxdbgCpuRegsGet)/* VxDBG interrupt exit rtn */
#endif /* _WRS_CONFIG_SMP */

 /* externs */

#ifdef _WRS_CONFIG_SMP
	DATA_IMPORT(vxKernelVars)
	DATA_IMPORT(vxdbgCpuCtrlVars)
	DATA_IMPORT(kernelStateLock)
	FUNC_IMPORT(kernelLockGive)	/* give kernel spinlock */
	FUNC_IMPORT(kernelLockTake)	/* take kernel spinlock, no int lock */
#else /*_WRS_CONFIG_SMP*/
	DATA_IMPORT(errno)
	DATA_IMPORT(intCnt)
	DATA_IMPORT(kernelState)
	DATA_IMPORT(taskIdCurrent)
	DATA_IMPORT(readyQHead)
	DATA_IMPORT(vxIntStackBase)
#endif /*_WRS_CONFIG_SMP*/

	DATA_IMPORT(_func_armIrqHandler)
#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ
	DATA_IMPORT(_func_armFiqHandler)
#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

	FUNC_IMPORT(reschedule)
#ifdef _WRS_CONFIG_PWR_MGMT
        FUNC_IMPORT(windPwrUp)
        DATA_IMPORT(_func_cpuPwrIntExitHook)
#endif /* _WRS_CONFIG_PWR_MGMT */
	FUNC_IMPORT(workQDoWork)

#ifdef _WRS_CONFIG_SV_INSTRUMENTATION
	FUNC_IMPORT(evtAction)
	FUNC_IMPORT(eventPointSwHandle)

#ifndef _WRS_CONFIG_SMP
	DATA_IMPORT(workQIsEmpty)
#endif /*_WRS_CONFIG_SMP*/

#endif /* _WRS_CONFIG_SV_INSTRUMENTATION */

#if (ARM_THUMB2)
	FUNC_IMPORT(vxTaskEntryFatalInject)
#endif /* (ARM_THUMB2) */

 /* variables */

     .data
     .balign 4


#ifndef _WRS_CONFIG_SMP

VAR_LABEL(intNestingLevel)
	.long	0

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ
VAR_LABEL(fiqNestingLevel)
	.long	0
#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

#ifdef CHECK_NESTING
VAR_LABEL(maxIntNestingLevel)
	.long	0
#endif /* CHECK_NESTING */

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ
VAR_LABEL(maxFiqNestingLevel)
	.long	0
#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

#endif /*_WRS_CONFIG_SMP*/

	.text
	.balign	4

/* PC-relative-addressable symbols - LDR Rn,=sym is broken */

#ifdef _WRS_CONFIG_SMP

L$_vxKernelVars:
	.long	VAR(vxKernelVars)

L$_vxdbgCpuCtrlVars:
	.long	VAR(vxdbgCpuCtrlVars)

L$_kernelStateLock:
	.long	VAR(kernelStateLock)

#else /*_WRS_CONFIG_SMP*/

 /*
  * L$_errno, L$_intCnt, L$_intNestingLevel MUST be together for
  * optimizations to work
  */

L$_errno:
	.long	VAR(errno)
L$_intCnt:
	.long	VAR(intCnt)
L$_intNestingLevel:
	.long	VAR(intNestingLevel)

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ
L$_fiqNestingLevel:
	.long	VAR(fiqNestingLevel)
#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

#ifdef CHECK_NESTING
L$_maxIntNestingLevel:
	.long	VAR(maxIntNestingLevel)

#  ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ
L$_maxFiqNestingLevel:
	.long	VAR(maxFiqNestingLevel)
#  endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */
#endif /* CHECK_NESTING */

L$_vxIntStackBase:
	.long	VAR(vxIntStackBase)

#endif /*_WRS_CONFIG_SMP*/

#ifdef _WRS_CONFIG_PWR_MGMT
L$_cpuPwrIntExitHook:
	.long	VAR(_func_cpuPwrIntExitHook)
#endif /* _WRS_CONFIG_PWR_MGMT */

L$__func_armIrqHandler:
	.long	VAR(_func_armIrqHandler)

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ
L$__func_armFiqHandler:
	.long	VAR(_func_armFiqHandler)
#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

#ifndef _WRS_CONFIG_SMP

L$_kernelState:
	.long	VAR(kernelState)

L$_kernelIsIdle:
	.long	VAR(kernelIsIdle)

L$_taskIdCurrent:
	.long	VAR(taskIdCurrent)

L$_readyQHead:
	.long	VAR(readyQHead)

#endif /*_WRS_CONFIG_SMP*/

#ifdef _WRS_CONFIG_SV_INSTRUMENTATION
L$_evtAction:
	.long	FUNC(evtAction)
L$_eventPointSwHandle:
	.long	FUNC(eventPointSwHandle)

#ifndef _WRS_CONFIG_SMP
L$_workQIsEmpty:
	.long	VAR(workQIsEmpty)
#endif /*_WRS_CONFIG_SMP*/

#endif /* _WRS_CONFIG_SV_INSTRUMENTATION */

#if (ARM_THUMB2)
	.thumb
#else /*(ARM_THUMB2)*/
	.code	32
#endif /*(ARM_THUMB2)*/

/*******************************************************************************
*
* intCpuLock - lock out interrupts on the local CPU
*
* By default, this routine disables IRQ interrupts to the local CPU but leaves
* the mask state of FIQ interrupts unchanged.  If unified FIQ/IRQ handling is
* enabled, FIQs and IRQs are disabled on the local CPU.  It returns the state
* of the CPSR I bit (and F bit, if enabled) as the lock-out key for the
* interrupt level prior to the call.  This should be passed back to the routine
* intCpuUnlock() to restore the previous interrupt level(s).
*
* EXAMPLE
* .CS
*     lockKey = intCpuLock ();
*
*      ...
*
*     intCpuUnlock (lockKey);
* .CE
*
* RETURNS
* The I bit (and F bit, if enabled) from the CPSR as the lock-out key for the
* interrupt level prior to the call.
*
* SEE ALSO: intCpuUnlock(), taskLock()
*
* int intCpuLock ()
*
*/

FUNC_BEGIN(intCpuLock)

#ifndef _WRS_CONFIG_SMP
FUNC_BEGIN(intLock)
#endif /* !_WRS_CONFIG_SMP */
	MRS	r1, cpsr		/* get current status */

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

	AND	r0, r1, #(I_BIT | F_BIT)	/* save bits to return in r0 */
	ORR	r1, r1, #(I_BIT | F_BIT)	/* disable IRQs and FIQs */

#else

	AND	r0, r1, #I_BIT		/* save bit to return in r0 */
	ORR	r1, r1, #I_BIT		/* disable IRQs but leave FIQs */

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

	MSR	cpsr, r1
	MOV     pc, lr

        FUNC_END(intCpuLock)
#ifndef _WRS_CONFIG_SMP
        FUNC_END(intLock)
#endif /* !_WRS_CONFIG_SMP */

/*******************************************************************************
*
* intCpuUnlock - cancel interrupt locks on the local CPU
*
* By default, this routine restores the enable state of IRQ interrupts on the
* local CPU that have been disabled by a call to the routine intCpuLock().
* If unified FIQ/IRQ handling is enabled, FIQs and IRQs are disabled on the
* local CPU. Use the lock-out key obtained from the preceding intCpuLock() call.
*
* RETURNS: N/A
*
* SEE ALSO: intCpuLock()
*
* void intCpuUnlock
*       (
*       int lockKey
*       )
*
*/

FUNC_BEGIN(intCpuUnlock)

#ifndef _WRS_CONFIG_SMP
FUNC_BEGIN(intUnlock)
#endif /* !_WRS_CONFIG_SMP */
	MRS	r1, cpsr		/* get current status */

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

	BIC	r1, r1, #(I_BIT | F_BIT)	/* clear I and F bits */
	AND	r0, r0, #(I_BIT | F_BIT)	/* clear bits except I and F */

#else

	BIC	r1, r1, #I_BIT		/* clear I bit */
	AND	r0, r0, #I_BIT		/* clear all bits passed except I bit */

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

	ORR	r1, r1, r0		/* OR in passed bit */
	MSR	cpsr, r1
	MOV     pc, lr

        FUNC_END(intCpuUnlock)
#ifndef _WRS_CONFIG_SMP
        FUNC_END(intUnlock)
#endif /* !_WRS_CONFIG_SMP */

/*******************************************************************************
*
* intCpuMicroLock - lock out interrupts on the local CPU
*
* By default, this routine disables IRQ interrupts to the local CPU but leaves
* the mask state of FIQ interrupts unchanged.  If unified FIQ/IRQ handling is
* enabled, FIQs and IRQs are disabled on the local CPU.  It returns the state
* of the CPSR I bit (and F bit, if enabled) as the lock-out key for the
* interrupt level prior to the call.  This should be passed back to the routine
* intCpuUnlock() to restore the previous interrupt level(s).
* Operations that may modify the CPSR control field (I,F,T,M[0:4]) may not be
* performed between the calls to intCpuMicroLock() and intCpuMicroUnlock(),
* otherwise unexpected results may occur.
*
* EXAMPLE
* .CS
*     lockKey = intCpuMicroLock ();
*
*      ...
*
*     intCpuMicroUnlock (lockKey);
* .CE
*
* RETURNS
* The CPSR, as the lock-out key for the interrupt level prior to the call.
*
* SEE ALSO: intCpuLock(), taskLock()
*
* NOMANUAL
*
* int intCpuMicroLock ()
*
*/

FUNC_BEGIN(intCpuMicroLock)
	MRS	r0, cpsr		/* get current status */

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

	ORR	r1, r0, #(I_BIT | F_BIT)	/* disable IRQs and FIQs */

#else

	ORR	r1, r0, #I_BIT		/* disable IRQs but leave FIQs */

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

	MSR	cpsr_c, r1              /* set new status */
	MOV	pc, lr

        FUNC_END(intCpuMicroLock)

/*******************************************************************************
*
* intCpuMicroUnlock - cancel interrupt locks on the local CPU
*
* This routine restores the control field (I,F,T,M[0:4]) of the CPSR.  By
* default, only the enable state of IRQ interrupts on the local CPU should have
* been modified/disabled by a previous call to the routine intCpuMicroLock().
* If unified FIQ/IRQ handling is enabled, FIQs and IRQs are modified/disabled
* by intCpuMicroLock().  The lock-out key obtained from the preceding
* intCpuMicroLock() call should be passed to this routine to restore the
* previous interrupt level.
* Operations that may modify the CPSR control field (I,F,T,M[0:4]) may not be
* performed between the calls to intCpuMicroLock() and intCpuMicroUnlock(),
* otherwise unexpected results may occur.
*
* RETURNS: N/A
*
* SEE ALSO: intCpuMicroLock()
*
* NOMANUAL
*
* void intCpuMicroUnlock
*       (
*       int lockKey
*       )
*
*/

FUNC_BEGIN(intCpuMicroUnlock)
	MSR     cpsr_c, r0              /* restore previous status */
	MOV	pc, lr

        FUNC_END(intCpuMicroUnlock)

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ
/*******************************************************************************
*
* intILock - lock out IRQ interrupts
*
* This routine disables IRQ interrupts to the CPU.  It returns
* the state of the CPSR I bit as the lock-out key for the
* interrupt level prior to the call and this should be passed back to the
* routine intIUnlock() to restore the previous interrupt level.
*
* Note that this routine is not a replacement for intCpuLock(); it should
* be used only by code that needs only IRQs disabled  (e.g. pre-emptive
* IRQ handling code).
*
* EXAMPLE
* .CS
*     lockKey = intILock ();
*
*      ...
*
*     intIUnlock (lockKey);
* .CE
*
* RETURNS
* The I bit from the CPSR as the lock-out key for the interrupt level
* prior to the call.
*
* SEE ALSO: intIUnlock(), intFLock(), intFUnlock(), intCpuLock()
*
* int intILock ()
*
*/

FUNC_BEGIN(intILock)
	MRS	r1, cpsr		/* get current status */
	AND	r0, r1, #I_BIT		/* save bit to return in r0 */
	ORR	r1, r1, #I_BIT		/* disable IRQs but leave FIQs */
	MSR	cpsr, r1
	MOV	pc, lr

        FUNC_END(intILock)

/*******************************************************************************
*
* intIUnlock - cancel IRQ interrupt lock
*
* This routine restores the enable state of IRQ interrupts that
* have been disabled by a call to the routine intILock().  Use the
* lock-out key obtained from the preceding intILock() call.
*
* Note that this routine is not a replacement for intCpuUnlock(); it
* should be used only by code that needs to change the IRQ state only
* (e.g. pre-emptive IRQ handling code).
*
* RETURNS: N/A
*
* SEE ALSO: intILock(), intFLock(), intFUnlock(), intCpuUnlock()
*
* void intIUnlock
*       (
*       int lockKey
*       )
*
*/

FUNC_BEGIN(intIUnlock)
	MRS	r1, cpsr		/* get current status */
	BIC	r1, r1, #I_BIT		/* clear IRQ bit */
	AND	r0, r0, #I_BIT		/* clear all bits passed except I bit */
	ORR	r1, r1, r0		/* OR in passed bit */
	MSR	cpsr, r1
	MOV	pc, lr

        FUNC_END(intIUnlock)

/*******************************************************************************
*
* intFLock - lock out FIQ interrupts
*
* This routine disables FIQ interrupts to the CPU.  It returns
* the state of the CPSR F bit as the lock-out key for the
* interrupt level prior to the call and this should be passed back to the
* routine intFUnlock() to restore the previous interrupt level.
*
* Note that this routine is not a replacement for intCpuLock(); it should
* be used only by code that needs only FIQs disabled  (e.g. pre-emptive
* FIQ handling code).
*
* EXAMPLE
* .CS
*     lockKey = intFLock ();
*
*      ...
*
*     intFUnlock (lockKey);
* .CE
*
* RETURNS
* The F bit from the CPSR as the lock-out key for the interrupt level
* prior to the call.
*
* SEE ALSO: intFUnlock(), intILock(), intIUnlock(), intCpuLock()
*
* int intILock ()
*
*/

FUNC_BEGIN(intFLock)
	MRS	r1, cpsr		/* get current status */
	AND	r0, r1, #F_BIT		/* save F bit to return in r0 */
	ORR	r1, r1, #F_BIT		/* disable FIQ */
	MSR	cpsr, r1
	MOV	pc, lr

        FUNC_END(intFLock)

/*******************************************************************************
*
* intFUnlock - cancel FIQ interrupt locks
*
* This routine restores the enable state of FIQ interrupts that
* have been disabled by a call to the routine intFLock().  Use the
* lock-out key obtained from the preceding intFLock() call.
*
* Note that this routine is not a replacement for intCpuUnlock(); it
* should be used only by code that needs to change the FIQ state only
* (e.g. pre-emptive FIQ handling code).
*
* RETURNS: N/A
*
* SEE ALSO: intFLock(), intILock(), intFUnlock(), intCpuUnlock()
*
* void intFUnlock
*       (
*       int lockKey
*       )
*
*/

FUNC_BEGIN(intFUnlock)
	MRS	r1, cpsr		/* get current status */
	BIC	r1, r1, #F_BIT		/* clear the FIQ bit */
	AND	r0, r0, #F_BIT		/* clear all bits passed except F */
	ORR	r1, r1, r0		/* OR in passed bit */
	MSR	cpsr, r1
	MOV	pc, lr

        FUNC_END(intFUnlock)

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

/*******************************************************************************
*
* intIFLock - lock out IRQ and FIQ interrupts
*
* This routine disables IRQ and FIQ interrupts to the CPU.  It returns
* the state of the CPSR I and F bits as the lock-out key for the
* interrupt level prior to the call and this should be passed back to the
* routine intIFUnlock() to restore the previous interrupt level.
*
* Note that this routine is not a replacement for intCpuLock(); it should
* be used only by code that needs FIQ disabled as well as IRQ (e.g. FIQ
* handling code).
*
* EXAMPLE
* .CS
*     lockKey = intIFLock ();
*
*      ...
*
*     intIFUnlock (lockKey);
* .CE
*
* RETURNS
* The I and F bits from the CPSR as the lock-out key for the interrupt level
* prior to the call.
*
* SEE ALSO: intIFUnlock(), intCpuLock(), intCpuUnlock(), taskLock()
*
* int intIFLock ()
*
*/

FUNC_BEGIN(intIFLock)
         MRS    r1, cpsr                /* get current status */
         AND    r0, r1, #I_BIT | F_BIT  /* save bits to return in r0 */
         ORR    r1, r1, #I_BIT | F_BIT  /* disable IRQ and FIQ */
         MSR    cpsr, r1
         MOV    pc, lr

         FUNC_END(intIFLock)

/*******************************************************************************
*
* intIFUnlock - cancel IRQ and FIQ interrupt locks
*
* This routine restores the enable state of IRQ and FIQ interrupts that
* have been disabled by a call to the routine intCpuLock().  Use the
* lock-out key obtained from the preceding intIFLock() call.
*
* Note that this routine is not a replacement for intCpuUnlock(); it
* should be used only by code that needs to change the FIQ state as well
* as IRQ (e.g. FIQ handling code).
*
* RETURNS: N/A
*
* SEE ALSO: intIFLock(), intCpuLock(), intCpuUnlock()
*
* void intIFUnlock
*       (
*       int lockKey
*       )
*
*/

FUNC_BEGIN(intIFUnlock)
	MRS	r1, cpsr		/* get current status */
	BIC	r1, r1, #I_BIT | F_BIT	/* clear IRQ and FIQ bits */
	AND	r0, r0, #I_BIT | F_BIT	/* clear all bits passed except I & F */
	ORR	r1, r1, r0		/* OR in passed bit */
	MSR	cpsr, r1
	MOV	pc, lr

        FUNC_END(intIFUnlock)

/*******************************************************************************
*
* intVBRSet - set the vector base register
*
* This routine should only be called in supervisor mode.
* It is not used on the ARM.
*
* NOMANUAL
*
* void intVBRSet (baseAddr)
*      FUNCPTR *baseAddr;       /@ vector base address @/
*
*/

FUNC_BEGIN(intVBRSet)
	MOV	pc, lr

        FUNC_END(intVBRSet)

/*******************************************************************************
*
* intEnt - enter an interrupt service routine
*
* This routine is installed on the IRQ or FIQ (if supported) vector and is
* called at the entrance to an interrupt service routine to change to an
* interrupt stack, save critical registers and errno, increment the kernel
* interrupt counter and call routines to handle the interrupt. It is
* installed on the vector by excVecInit.
*
* This routine can NEVER be called from C.
*
* SEE ALSO: excVecInit(2)
*
* void intEnt ()
*
* INTERNAL
* It is expected that interrupt service routines will run with
* interrupts reenabled. The IRQ/FIQ mode of ARM has its own stack pointer
* but its r14 would be overwritten if the interrupt handler were
* reentered. To get around this problem, this routine switches to SVC
* mode (switching to a separate interrupt stack if necessary) before
* calling any interrupt service code. The IRQ/FIQ stack pointer points to a
* stack which is used before the switch to SVC mode. If unified IRQ/FIQ
* handling is supported, the IRQ/FIQ stack is eight words for every level of
* nesting of interrupts (CPSR,SPSR,r0-r4,PC).  if only IRQ handling is
* supported, the IRQ stack needs to be seven words for every level of nesting
* of interrupts (SPSR,r0-r4,PC).
*
* When updating this routine, please double-check the potential impact on
* intVxdbgCpuRegsGet() which retrieves the registers saved by intEnt() on
* the IRQ stack and on the SVC mode stack.
*
* NOTE: FIQ may be handled by this routine if unified IRQ/FIQ handling is
* selected.  Otherwise FIQ cannot use VxWorks facilities.
*
*/

FUNC_BEGIN(intEnt)

    /*
     * Entered directly from the hardware vector (via LDR pc, [])
     * Adjust return address so it points to instruction to resume
     */
	SUB	lr, lr, #4  /* adjust */

	/* save regs on IRQ (or FIQ, if supported) stack */

	STMFD	sp!, {r0-r4,lr}
	MRS	r0, spsr
	STMFD	sp!, {r0}

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

	MRS	r1, cpsr
	STMFD	sp!, {r1}	/* save CPSR, with IRQ or FIQ mode, on stack */

	/* save sp in non-banked reg so can access saved regs from SVC mode */

	MOV	r2, sp

	/*
	 * switch to SVC mode with IRQs/FIQs disabled (they should be already)
	 * Note this can be done without clearing the mode bits before ORRing
	 */

        BIC     r1, r1, #MASK_MODE
	ORR	r1, r1, #(MODE_SVC32 | I_BIT | F_BIT)
	MSR	cpsr, r1

#else

	/* save sp in non-banked reg so can access saved regs from SVC mode */

	MOV	r2, sp

	/*
	 * switch to SVC mode with IRQs disabled (they should be already)
	 * Note this can be done without clearing the mode bits before ORRing
	 */

	MRS	r1, cpsr
        BIC     r1, r1, #MASK_MODE
	ORR	r1, r1, #(MODE_SVC32 | I_BIT)
	MSR	cpsr, r1

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

	/*
	 * INTERRUPTS DISABLED
	 *
	 * r0 = [scratch]
	 * r1 = [scratch]
	 * r2 = irq_sp (or fiq_sp, if supported)
	 * r3 = [scratch]
	 * r4 = [scratch]
	 * lr = lr of interrupted svc process
	 * MODE: SVC
	 *
	 * bump our interrupt nesting level counter - we have to use
	 * this rather than the kernel's counter (intCnt) because the
	 * kernel sometimes increments its counter to fake interrupt
	 * context and we need something to tell us when we should
	 * change stacks
	 */


#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

	LDR	r0, [r2]				/* irq_CPSR/fiq_CPSR */
	TST	r0, #(MODE_FIQ32 & MASK_SUBMODE)	/* FIQ or IRQ? */
	BNE	fiq_nestlvl

	/*
	 * IRQ:
	 * r3 = &intNestingLevel
	 * r0 = intNestingLevel
	 */
	_ARM_PER_CPU_VALUE_AND_ADRS_GET(r0, r3, intNestingLevel)
	ADD	r0, r0, #1
	STR	r0, [r3]

#ifdef CHECK_NESTING
	_ARM_PER_CPU_VALUE_GET (r1, r3, maxIntNestingLevel)
	CMP	r0, r1
	BLS	irq_not_deeper
	_ARM_PER_CPU_ADRS_GET (r1, r3, maxIntNestingLevel)
	STR     r0, [r1]       /* update with the larger value */
irq_not_deeper:

#endif /* CHECK_NESTING */

	B	fiq_not_deeper

fiq_nestlvl:

	/*
	 * FIQ:
	 * r3 = &fiqNestingLevel
	 * r0 = fiqNestingLevel
	 */
	_ARM_PER_CPU_VALUE_AND_ADRS_GET(r0, r3, fiqNestingLevel)
	ADD	r0, r0, #1
	STR	r0, [r3]

#ifdef CHECK_NESTING
	_ARM_PER_CPU_VALUE_GET (r1, r3, maxFiqNestingLevel)
	CMP	r0, r1
	BLS	fiq_not_deeper
	_ARM_PER_CPU_ADRS_GET (r1, r3, maxFiqNestingLevel)
	STR     r0, [r1]       /* update with the larger value */
#endif /* CHECK_NESTING */

fiq_not_deeper:

#else /* IRQ only */

	/* r3 = &intNestingLevel */
	/* r0 = intNestingLevel */
	_ARM_PER_CPU_VALUE_AND_ADRS_GET(r0, r3, intNestingLevel)
	ADD	r0, r0, #1
	STR	r0, [r3]

#ifdef CHECK_NESTING
	_ARM_PER_CPU_VALUE_GET (r1, r3, maxIntNestingLevel)
	CMP	r0, r1
	BLS	not_deeper
	_ARM_PER_CPU_ADRS_GET (r1, r3, maxIntNestingLevel)
	STR     r0, [r1]       /* update with the larger value */
not_deeper:
#endif /* CHECK_NESTING */

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

	/* switch to SVC-mode interrupt stack if not already using it */

	MOV	r1, sp				/* save svc_sp */
	TEQ	r0, #1				/* first level of nesting? */

#ifdef _WRS_CONFIG_SMP

	BNE     not_first_level
	_ARM_PER_CPU_VALUE_GET_SPLR (sp, r4, vxIntStackBase)
not_first_level:

	/* get errno and &intCnt */

	_ARM_PER_CPU_VALUE_GET (r0, r4, errno)
	_ARM_PER_CPU_ADRS_GET (r3, r4, intCnt)

#else /*_WRS_CONFIG_SMP*/

	LDR	r4, L$_vxIntStackBase	        /* ...yes, change stack */
	LDREQ	sp, [r4]

	/* get errno and &intCnt */

	ADR	r0, L$_errno
	LDMIA	r0, {r0,r3}	/* get pointers to errno and intCnt */
	LDR	r0, [r0]	/* r0 = errno */

#endif /*_WRS_CONFIG_SMP*/

 /*
  * save errno and registers on stack
  * r0 = errno
  * r1 = svc_sp
  * r2 = irq_sp (or fiq_sp, if supported)
  * r3-> intCnt
  * lr = lr of interrupted svc process
  */

        STMFD sp!, {r0-r2,r12,lr} /* errno, save svc_sp, irq_sp/fiq_sp, */
                                  /* svc_r12, svc_lr */

 /*
  * If unified IRQ/FIQ handling is enabled (#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ):
  *
  * IRQ/FIQ stack contains
  *    irq_CPSR/fiq_CPSR
  *    irq_SPSR/fiq_SPSR
  *    r0-r4
  *    irq_lr/fiq_lr
  *
  * SVC interruptStack contains
  *    errno
  *    svc_sp
  *    irq_sp/fiq_sp
  *    svc_r12
  *    svc_lr when changed from IRQ mode to SVC mode
  *
  * Otherwise:
  *
  * IRQ stack contains
  *    irq_SPSR
  *    r0-r4
  *    irq_lr
  *
  * SVC interruptStack contains
  *    errno
  *    svc_sp
  *    irq_sp
  *    svc_r12
  *    svc_lr when changed from IRQ mode to SVC mode
  *
  * Carry on with normal intEnt stuff - IRQs still disabled
  */

	LDR	lr, [r3]	/* increment kernel interrupt counter */
	ADD	lr, lr, #1
	STR	lr, [r3]

	/*
	 * _WRS_CONFIG_SV_INSTRUMENTATION
	 * WindView Instrumentation for this architecure is performed
	 * single-step in intArchLib.c. No action is required here.
	 */

#ifdef _WRS_CONFIG_PWR_MGMT
#  ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

	LDR	r0, [r2]		/* irq_CPSR/fiq_CPSR */
	TST	r0, #(MODE_FIQ32 & MASK_SUBMODE)	/* FIQ or IRQ? */
	BNE	get_fiq_nestlvl

        /* first level of nesting? */
	_ARM_PER_CPU_VALUE_AND_ADRS_GET (r0, r1, intNestingLevel)
	B	got_nestlvl

get_fiq_nestlvl:
	_ARM_PER_CPU_VALUE_AND_ADRS_GET (r0, r1, fiqNestingLevel)

got_nestlvl:
#  else

        /* first level of nesting? */
	_ARM_PER_CPU_VALUE_AND_ADRS_GET (r0, r1, intNestingLevel)

#  endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

        /* notify power management */
        TEQ     r0, #1                  /* first level of nesting? */
        BNE     intEntPwrHooksSkip
        BL      FUNC(windPwrUp)         /* call power up function */

intEntPwrHooksSkip:
#endif /* _WRS_CONFIG_PWR_MGMT */

        /* call interrupt handler, via function pointer */

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

	LDR	r0, [sp, #8]		/* irq_sp/fiq_sp */
	LDR	r0, [r0]		/* irq_CPSR/fiq_CPSR */
	TST	r0, #(MODE_FIQ32 & MASK_SUBMODE)	/* FIQ or IRQ? */
	BNE	get_fiq_handler

        LDR r0, L$__func_armIrqHandler /* get IRQ handler pointer */
	B	got_handler

get_fiq_handler:
        LDR r0, L$__func_armFiqHandler /* get FIQ handler pointer */

got_handler:
#else

        LDR r0, L$__func_armIrqHandler /* get IRQ handler pointer */

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

        ADR lr, FUNC(intExit)   /* set return address */

#if (ARM_THUMB2)
        ORR lr, lr, #1              /* set LSB to force Thumb mode */
#endif /* (ARM_THUMB2) */
        LDR pc, [r0]

        FUNC_END(intEnt)

/*******************************************************************************
*
* intExit - exit an interrupt service routine
*
* Check the kernel ready queue to determine if rescheduling is necessary.  If
* no higher priority task has been readied, and no kernel work has been queued,
* then we return to the interrupted task.
*
* If rescheduling is necessary, the context of the interrupted task is saved
* in its associated TCB.
*
* This routine must be branched to when exiting an interrupt service routine.
* This normally happens automatically, as the return address for the
* higher-level IRQ handler is set to intExit by intEnt.
*
* This routine can NEVER be called from C.
*
* SEE ALSO: intConnect(2)
*
* void intExit ()
*
* INTERNAL
*
* REGISTERS
*    lr trashable because this routine does not return
*    r0-r4 trashable because they're on the stack
*
* If unified IRQ/FIQ handling is enabled (#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ):
*
* IRQ/FIQ stack contains
*    irq_CPSR/fiq_CPSR
*    irq_SPSR/fiq_SPSR
*    r0-r4
*    irq_lr/fiq_lr
*
* SVC interruptStack contains
*    errno
*    svc_sp
*    irq_sp/fiq_sp
*    svc_r12
*    svc_lr when changed from IRQ mode to SVC mode
*
* Otherwise:
*
* IRQ stack contains
*    irq_SPSR
*    r0-r4
*    irq_lr
*
* SVC interruptStack contains
*    errno
*    svc_sp
*    irq_sp
*    svc_r12
*    svc_lr when changed from IRQ mode to SVC mode
*/

FUNC_BEGIN(intExit)
FUNC_BEGIN(ARM_intExit)

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ
        /* disable IRQs & FIQs */

        MRS     r1, cpsr
        ORR     r1, r1, #(I_BIT | F_BIT)
        MSR     cpsr, r1

#else
        /* disable IRQs */

        MRS     r1, cpsr
        ORR     r1, r1, #I_BIT
        MSR     cpsr, r1

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

        /* INTERRUPTS DISABLED */

        /* restore errno from stack */

        LDMFD sp!, {lr}
        _ARM_PER_CPU_ADRS_GET(r0, r1, errno)
        STR     lr, [r0]      /* restore errno */

#ifdef _WRS_CONFIG_PWR_MGMT
#  ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

	LDR	r0, [r2]		/* irq_CPSR/fiq_CPSR */
	TST	r0, #(MODE_FIQ32 & MASK_SUBMODE)	/* FIQ or IRQ? */
	BNE	get_fiq_nestlvl

        /* first level of nesting? */
	_ARM_PER_CPU_VALUE_AND_ADRS_GET (r0, r1, intNestingLevel)
	B	got_nestlvl

get_fiq_nestlvl:
	_ARM_PER_CPU_VALUE_AND_ADRS_GET (r0, r1, fiqNestingLevel)

got_nestlvl:
#  else

        /* first level of nesting? */
	_ARM_PER_CPU_VALUE_AND_ADRS_GET (r0, r1, intNestingLevel)

#  endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

        /* notify power management */
        TEQ     r0, #1                  /* first level of nesting? */
        LDR     r0, L$_cpuPwrIntExitHook
	LDR	r0, [r0]               /* get callback function */
        TEQ     r0, #0                 /* test NULL function */
        BEQ     intExitPwrHooksSkip
	MOV	lr, pc        /* save return address */
#if (ARM_THUMB2)
	ADD	lr, lr, #5    /* Thumb-2 set LSB (and account for ADD) */
#endif /* (ARM_THUMB2) */
	MOV	pc, r0       /* ARM/Thumb-2 call cpuPwrIntExitHook */
intExitPwrHooksSkip:
#endif /* _WRS_CONFIG_PWR_MGMT */

#ifdef _WRS_CONFIG_SV_INSTRUMENTATION

        /*
         * system viewer instrumentation - BEGIN
         * log event if work has been done in the interrupt handler.
         */

        LDR r0, L$_evtAction /* is instrumentation on? */
        LDR r0, [r0]
        TEQ r0, #0
        BNE instrumentIntExit /* branch if so */

        /* instrumentation currently disabled */

resumeIntExit:

       /* system viewer instrumentation - END */
#endif /* _WRS_CONFIG_SV_INSTRUMENTATION */


        /*
         * Check our private interrupt nesting counter.
         * If nested, RTI.
         *
         * We don't rely on the global variable <intCnt> because
         * windTickAnnounce() modifies it to fake an ISR context.
         */

#  ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

	LDR	lr, [sp, #4]		/* irq_sp/fiq_sp */
	LDR	r2, [lr]		/* r2 = irq_CPSR/fiq_CPSR */
	TST	r2, #(MODE_FIQ32 & MASK_SUBMODE)	/* FIQ or IRQ? */
	BNE	get_fiq_nestlvl_1

        /* first level of nesting? */
	_ARM_PER_CPU_VALUE_AND_ADRS_GET (r0, r1, intNestingLevel)
	B	got_nestlvl_1

get_fiq_nestlvl_1:
	_ARM_PER_CPU_VALUE_AND_ADRS_GET (r0, r1, fiqNestingLevel)

got_nestlvl_1:

	CMP	r0, #1
	BGT	intExit_RTI

        /*
         * not kernelState
         *
         * If the interrupt occurred during an interrupt entry sequence, we're
         * nested so RTI.
         * Similarly, if the interrupt occurred during an exception entry
         * sequence, we can't easily save the context for a task switch so RTI.
         * These cases are handled by simply testing the CPU mode.
         */

	LDR     lr, [lr, #4]                        /* get irq_SPSR */

#  else

	_ARM_PER_CPU_VALUE_GET (r0, lr, intNestingLevel)

	CMP	r0, #1
	BGT	intExit_RTI

 /*
  * not kernelState
  *
  * If the interrupt occurred during an interrupt entry sequence, we're
  * nested so RTI.
  * Similarly, if the interrupt occurred during an exception entry
  * sequence, we can't easily save the context for a task switch so RTI.
  * These cases are handled by simply testing the CPU mode.
  */

        LDR     lr, [sp, #4]                    /* get irq_sp */
        LDR     lr, [lr]                        /* get irq_SPSR */

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

        AND     lr, lr, #MASK_SUBMODE           /* check mode bits */

        TEQ     lr, #MODE_SVC32 & MASK_SUBMODE  /* if !SVC(3), check for USR */
        BEQ     cont_intExit

        TEQ     lr, #MODE_USER32 & MASK_SUBMODE /* if USR (0), cont_intExit */
        BNE     intExit_RTI                     /* if not SVC or USR, RTI */

cont_intExit:

#ifdef _WRS_CONFIG_SMP

        /* local interrupts are locked */

        /*
         * r0 = kernel lock owner
         * r1 = current cpu index
         */

        _ARM_KERNEL_LOCK_OWNER_AND_INDEX_GET(r0, r1)
        CMP     r0, r1		/* myself kernel lock owner? */
        BEQ     intExit_RTI	/* if yes, just return */

        _ARM_PER_CPU_VALUE_GET(r0, lr, taskIdCurrent)

        /*
         * For SMP, we'll check per CPU variable "reschedMode"
         *
         * If it's not WIND_NO_RESCHEDULE, branch to "saveIntContext"
         * and "reschedule()" regardless of the lockCnt value and
         * status of taskIdCurrent.
         * reschedule() will check these states.
         *
         * If it's WIND_NO_RESCHEDULE, fall through and check the workQ
         */

        _ARM_PER_CPU_VALUE_GET(r1, lr, reschedMode)
        TEQ     r1, #0
        BNE     saveIntContext

        /* local interrupts are locked: check whether workQ is empty or not */

        _ARM_PER_CPU_VALUE_GET(r0, lr, workQIsEmpty)
        TEQ     r0, #0		/* test for work to do */
        BNE     intExit_RTI	/* if no, exit with RTI */

        /* unlock local interrupts */

        MRS     r0, cpsr

#  ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

        BIC     r0, r0, #(I_BIT | F_BIT)

#  else

        BIC     r0, r0, #I_BIT

#  endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

        MSR     cpsr, r0

        /* take the kernel lock */

        BL      FUNC(kernelLockTake)

emptyWorkQueue:

        /* redundant on first pass ---unlock local interrupts */

        MRS     r0, cpsr

#  ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

        BIC     r0, r0, #(I_BIT | F_BIT)

#  else

        BIC     r0, r0, #I_BIT

#  endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

       MSR     cpsr, r0

        /* Drain the workQ since there are jobs */

        BL      FUNC(workQDoWork)

        /* re-lock local interrupts before emptying workQ */

        MRS     r0, cpsr

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

        ORR     r0, r0, #(I_BIT | F_BIT)

#else

        ORR     r0, r0, #I_BIT

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

        MSR     cpsr, r0

        _ARM_PER_CPU_VALUE_GET(r0, lr, workQIsEmpty)
        TEQ     r0, #0			/* test for work to do */
        BEQ     emptyWorkQueue		/* workQ is not empty */

       /* exit kernel (kernelState = FALSE) -- release the spin lock */

        BL      FUNC(kernelLockGive)

        /*
         * Local interrupts are locked and workQ is empty.
         * Recheck "reschedMode":
         * Branch to "saveIntContext" if it's not WIND_NO_RESCHEDULE
         * (state changed), otherwise branch to "intExit_RTI".
         */

        _ARM_PER_CPU_VALUE_GET(r0, lr, reschedMode)
        TEQ     r0, #0
        BEQ     intExit_RTI

        _ARM_PER_CPU_VALUE_GET(r0, lr, taskIdCurrent)
        B       saveIntContext

#else  /* _WRS_CONFIG_SMP */

 /*
  * interrupts were not nested
  *
  * if kernelState, RTI
  */

	LDR	r0, L$_kernelState	/* if kernelState == TRUE */
	LDR	lr, [r0]
	TEQ	lr, #0
	BNE	intExit_RTI		/* exit */

 /* if (current task == highest ready task), RTI */

        _ARM_PER_CPU_VALUE_GET (r0, r1, taskIdCurrent)

        /* If Idle */

        _ARM_PER_CPU_VALUE_GET_SPLR (lr, r1, kernelIsIdle)
	TEQ	lr, #0
	BNE	exitKerIdle		/* exit */

	LDR	r1, L$_readyQHead	/* is it head of readyQ? */
	LDR	r1, [r1]
	ADD	lr, r0, #WIND_TCB_QNODE	/* adjust so lr -> qNode in TCB */
	TEQ	lr, r1
	BEQ	intExit_RTI		/* RTI if yes */

        /*
         * current task is NOT highest priority ready task
         * check if allowed to preempt this task
         * r0 -> TCB
         * if (current task lock count != 0)
         *    // task preemption not allowed
         *    if (current task status == READY)
         *        RTI
         */

	LDR	lr, [r0, #WIND_TCB_LOCK_CNT]	/* task preemption allowed? */
	TEQ	lr, #0
	BEQ	saveIntContext			/* save context if yes */

        /* task preemption not allowed - is task ready? */

	LDR	lr, [r0, #WIND_TCB_STATUS]	/* check status */
	TEQ	lr, #WIND_READY
	BNE	saveIntContext			/* save context if not ready */

        /*
         * Current task cannot be preempted and is ready to run:
         * fall through to intExit_RTI
         */

#endif  /* _WRS_CONFIG_SMP */

intExit_RTI:

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

	LDR	lr, [sp, #4]		/* irq_sp/fiq_sp */
	LDR	r3, [lr]		/* r3 = irq_CPSR/fiq_CPSR */

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

#ifdef _WRS_CONFIG_SMP

        _ARM_PER_CPU_ADRS_GET (r0, lr, intCnt)

#  ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

	TST	r3, #(MODE_FIQ32 & MASK_SUBMODE)	/* FIQ or IRQ? */
	BNE	get_fiq_nestlvl_2

        _ARM_PER_CPU_ADRS_GET (r1, lr, intNestingLevel)
	B	got_nestlvl_2

get_fiq_nestlvl_2:
	_ARM_PER_CPU_ADRS_GET (r1, lr, fiqNestingLevel)

got_nestlvl_2:

#  else

        _ARM_PER_CPU_ADRS_GET (r1, lr, intNestingLevel)

#  endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

#else /*_WRS_CONFIG_SMP*/

#  ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

	TST	r3, #(MODE_FIQ32 & MASK_SUBMODE)	/* FIQ or IRQ? */
	BNE	get_fiq_nestlvl_2

	ADR	r0, L$_intCnt
	LDMIA	r0, {r0, r1}	/* get pointers to intCnt, intNestingLevel */
	B	got_nestlvl_2

get_fiq_nestlvl_2:
	/*
	 * Get pointers to intCnt and fiqNestingLevel.
	 * (The pointers are loaded seperately because they are not stored
	 *  in consecutive locations, as they are with intNestingLevel.)
	 */
	LDR	r0, L$_intCnt
        LDR	r1, L$_fiqNestingLevel

got_nestlvl_2:

#  else

	ADR	r0, L$_intCnt
	LDMIA	r0, {r0, r1}	/* get pointers to intCnt, intNestingLevel */

#  endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

#endif /*_WRS_CONFIG_SMP*/

	/*
	 * r0-> intCnt
	 * r1-> intNestingLevel/fiqNestingLevel
	 *
	 * decrement kernel interrupt counter and
	 * private interrupt nesting counter
	 */

	LDR	lr, [r0]
	SUB	lr, lr, #1
	STR	lr, [r0]

	LDR	lr, [r1]
	SUB	lr, lr, #1
	STR	lr, [r1]

	/*
	 * IRQs still disabled
	 * restore SVC-mode regs before changing back to IRQ mode
	 */

	LDMFD	sp!, {r1-r2,r12,lr}

	/*
	 * r1  = svc_sp (original)
	 * r2  = irq_sp/fiq_sp
	 * r12 = svc_r12 (original)
	 * lr  = svc_lr (original)
	 *
	 * SVC-mode interruptStack now flattened
	 * Switch SVC-mode stack back to what it was when the IRQ occurred
	 */

	MOV	sp, r1

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

	MRS	r0, cpsr
	BIC	r0, r0, #MASK_MODE

	TST	r3, #(MODE_FIQ32 & MASK_SUBMODE)	/* FIQ or IRQ? */
	BNE	set_fiq_rtn

       /* return to IRQ mode with FIQs/IRQs disabled */

	ORR	r0, r0, #(MODE_IRQ32 | I_BIT | F_BIT)
	B	set_rtn_mode

set_fiq_rtn:
       /* return to FIQ mode with FIQs/IRQs disabled */

	ORR	r0, r0, #(MODE_FIQ32 | I_BIT | F_BIT)

set_rtn_mode:
	MSR	cpsr, r0

	/* adjust irq_sp/fiq_sp to point to SPSR */
	ADD	sp, sp, #4

#else

       /* return to IRQ mode with IRQs disabled */

	MRS	r0, cpsr
	BIC	r0, r0, #MASK_MODE
	ORR	r0, r0, #(MODE_IRQ32 | I_BIT)
	MSR	cpsr, r0

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

	/*
	 * now in IRQ/FIQ mode
	 * restore registers and return from interrupt
	 */

	LDMFD	sp!, {r0}  /* restore SPSR */

	_ARM_SPSR_SET(r0)
	
#if (!ARM_THUMB2)
        /* pull r0-r4, and PC from IRQ/FIQ stack and return */
	LDMFD	sp!, {r0-r4,pc}^	/* restore regs and return from intr */
#else /* (!ARM_THUMB2) */

/*
 * TODO: look at optimization possibilities in intEnt
 *      this seems very inefficient because PC and CPSR are not saved on the
 *      stack next to each other.
 */

        LDMIA   sp!,{r0-r4}             /* restore r0-r4 from IRQ/FIQ stack */

/* Now sp(off by 4 bytes) = pc */

        /* setup IRQ stack to become: PC, CPSR */
        LDR     lr, [sp]
        SUB     sp, sp, #4
        STR     lr, [sp]
        MRS     lr, spsr
        STR     lr, [sp, #4]

        /* pc and CPSR are still on stack */

        RFEIA   sp!              /* dispatch: restoring PC & CPSR */
#endif /* (!ARM_THUMB2) */

saveIntContext:

	/*
	 * interrupt occurred during task code and the task is either
	 * blocked or preemption is allowed so we're going to reschedule
	 *
	 * IRQs still disabled
	 * r0 -> TCB
	 */

	/* recover registers from SVC-mode interrupt stack */

	LDMFD	sp!, {r1-r2,r12,lr}

	/*
	 * r0 -> TCB
	 * r1  = svc_sp (original)
         * r2  = irq_sp/fiq_sp
         * r12 = svc_r12 (original)
         * lr  = svc_lr (original)
         *
         * SVC-mode interruptStack now flattened
         * Switch SVC-mode stack back to what it was when the IRQ/FIQ occurred
         */

	MOV	sp, r1	/* restore original svc_sp */

        /* store registers in the TCB of the current task */

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

        LDR     r3, [r2, #4]            /* get task's CPSR from IRQ/FIQ stack */

#else

        LDR     r3, [r2]                /* get task's CPSR from IRQ/FIQ stack */

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

        ADD     r1, r0, #WIND_TCB_R5            /* r1 -> regs.r[5] */

        AND     r3, r3, #MASK_SUBMODE           /* EQ => USR mode */
        TEQ     r3, #MODE_USER32 & MASK_SUBMODE /* USR (0) */
        BNE     skip_usr

        /* USR MODE */
#if (!ARM_THUMB2)
        STMIA   r1, {r5-r12,sp,lr}^             /* store r5-r12, usr_[sp,lr] */
#else /*  (!ARM_THUMB2) */
        /* TODO: User mode not supported by Thumb-2 */
assert1:
        BL      FUNC(vxTaskEntryFatalInject)    /* vxTaskEntryFatalInject () */
                                                /* should not return */
	B       assert1                         /* loop forever */
#endif /*  (!ARM_THUMB2) */
        B       skip_svc

skip_usr:

#if (!ARM_THUMB2)

        /* SVC MODE */
        STMIA   r1, {r5-r12,sp,lr} /* store r5-r12, svc_[sp,lr] */

#else /* (!ARM_THUMB2) */

        /* SVC MODE */
        STMIA   r1, {r5-r12}       /* store r5-r12, svc_[sp,lr] */
        STR     sp, [r1,#4*8]      /* sp illegal in THUMB-2 STM reg list  */
        STR     lr, [r1,#4*9]

#endif /* (!ARM_THUMB2) */


skip_svc:
	/* pull CPSR, r0-r4, and PC from IRQ/FIQ stack */

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

        LDMIB   r2, {r4-r10}                    /* get task's CPSR,r0-r4,PC */

#else

        LDMIA   r2, {r4-r10}                    /* get task's CPSR,r0-r4,PC */

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

        STR     r4, [r0, #WIND_TCB_CPSR]        /* put CPSR in TCB */
        STMDB   r1, {r5-r9}                     /* put r0-r4 in TCB */
        STR     r10, [r0, #WIND_TCB_PC]         /* put PC in TCB */

#ifndef _WRS_CONFIG_WRHV_COPROCESSOR_CTRL
#ifndef ARMCPUMMULESS
        MRC     CP_MMU, 0, r1, c2, c0, 0   /* get CP_15_TTBase */
        STR     r1, [r0, #WIND_TCB_TTBASE] /* write to TCB */
#endif /*ARMCPUMMULESS*/
#endif /* _WRS_CONFIG_WRHV_COPROCESSOR_CTRL */

        /* Interrupts are still locked */

#ifdef _WRS_CONFIG_SMP

        /*
	 * For SMP, the scheduler runs on idle task's exception stack, so
         * r0 becomes idleTaskId.
	 */

        _ARM_PER_CPU_VALUE_GET(r0, r3, idleTaskId)

#else

        /*
         * For UP, the scheduler runs on current task's exception stack.
         * In this case, r0 is still taskIdCurrent.
	 */

#endif  /* _WRS_CONFIG_SMP */

        /* reset svc_sp to base of exception stack */

        LDR     r3, [r0, #WIND_TCB_EXC_CNT]       /* get excCnt */

        TEQ     r3, #0                            /* already on exc stack? */
        LDREQ   sp, [r0, #WIND_TCB_P_K_STK_BASE]  /* no? switch to exc stack */

        ADD     r3, r3, #1                        /* increment excCnt */
        STR     r3, [r0, #WIND_TCB_EXC_CNT]       /* and store in TCB */


        /* change to IRQ mode to flatten its stack */

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

	MRS	r1, cpsr			/* save mode and intr state */
        BIC     r3, r1, #MASK_MODE

	LDR	r4, [r2]			/* r4 = irq_cpsr/fiq_cpsr */

	TST	r4, #(MODE_FIQ32 & MASK_SUBMODE)	/* FIQ or IRQ? */
	BNE	set_fiq_mode

       /* change to IRQ mode with IRQs/FIQs still disabled */

	ORR	r3, r3, #(MODE_IRQ32 | I_BIT | F_BIT)
	B	set_mode

set_fiq_mode:
       /* change to FIQ mode with IRQs/FIQs still disabled */

	ORR	r3, r3, #(MODE_FIQ32 | I_BIT | F_BIT)

set_mode:
	MSR	cpsr, r3

        ADD     sp, sp, #4*8   /* flatten stack */

        /* back to SVC mode, IRQs/FIQs still disabled */

        ORR     r1, r1, #(I_BIT | F_BIT)        /* interrupts still disabled */
        MSR     cpsr, r1   /* r1 = CPSR */

#else

	MRS	r1, cpsr			/* save mode and intr state */
        BIC     r3, r1, #MASK_MODE
        ORR     r3, r3, #MODE_IRQ32 | I_BIT	/* interrupts still disabled */
        MSR     cpsr, r3
        ADD     sp, sp, #4*7   /* flatten stack */

        /* back to SVC mode, IRQs still disabled */

        ORR     r1, r1, #I_BIT                  /* interrupts still disabled */
        MSR     cpsr, r1   /* r1 = CPSR */

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

#ifdef _WRS_CONFIG_SMP

        /* unlock local interrupts before taking kernelLock */

        MRS     r1, cpsr

#  ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

        BIC     r1, r1, #(I_BIT | F_BIT)

#  else

        BIC     r1, r1, #I_BIT

#  endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

        MSR     cpsr, r1

 /* take the kernel lock */

        BL      FUNC(kernelLockTake)

 /* re-lock local interrupts */

        MRS     r1, cpsr

#  ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

        ORR     r1, r1, #(I_BIT | F_BIT)

#  else

        ORR     r1, r1, #I_BIT

#  endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

        MSR     cpsr, r1	/* r1 = CPSR */

#else   /* _WRS_CONFIG_SMP */

 /* set kernelState = TRUE */
	MOV	lr, #1
	LDR	r3, L$_kernelState
	STR	lr, [r3]

#endif  /* _WRS_CONFIG_SMP */

       /* clear intCnt and intNestingLevel */

#ifdef _WRS_CONFIG_SMP

	_ARM_PER_CPU_ADRS_GET (r2, lr, intCnt)

#  ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

        /* r4 =  irq_cpsr/fiq_cpsr */

	TST	r4, #(MODE_FIQ32 & MASK_SUBMODE)	/* FIQ or IRQ? */
	BNE	get_fiq_nestlvl_3

        _ARM_PER_CPU_ADRS_GET (r3, lr, intNestingLevel)
	B	got_nestlvl_3

get_fiq_nestlvl_3:
	_ARM_PER_CPU_ADRS_GET (r3, lr, fiqNestingLevel)

got_nestlvl_3:

#  else

	_ARM_PER_CPU_ADRS_GET (r3, lr, intNestingLevel)

#  endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

#else /*_WRS_CONFIG_SMP*/

#  ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

        /* r4 =  irq_cpsr/fiq_cpsr */

	TST	r4, #(MODE_FIQ32 & MASK_SUBMODE)	/* FIQ or IRQ? */
	BNE	get_fiq_nestlvl_3

	ADR	r2, L$_intCnt
	LDMIA	r2, {r2,r3}	/* get pointers to intCnt and intNestingLevel */
	B	got_nestlvl_3

get_fiq_nestlvl_3:
	/*
	 * Get pointers to intCnt and fiqNestingLevel.
	 * (The pointers are loaded seperately because they are not stored
	 *  in consecutive locations, as they are with intNestingLevel.)
	 */
	LDR	r2, L$_intCnt
        LDR	r3, L$_fiqNestingLevel

got_nestlvl_3:

#  else

	ADR	r2, L$_intCnt
	LDMIA	r2, {r2,r3}	/* get pointers to intCnt and intNestingLevel */

#  endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

#endif /*_WRS_CONFIG_SMP*/

	MOV	lr, #0
	STR	lr, [r2]
	STR	lr, [r3]

  /* now back to SVC mode with interrupts enabled */

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

        BIC     r1, r1, #(I_BIT | F_BIT)

#else

	BIC	r1, r1, #I_BIT   /* enable interrupts */

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

	MSR	cpsr, r1

	B	FUNC(reschedule)

#ifndef _WRS_CONFIG_SMP

exitKerIdle:
        /*
         * The kernel was in the idle loop so there is no context to be
         * saved.  We simply need to get back to reschedule which will
         * detect if there is something in the ready queue.
         *
         * Stack: reschedule uses the exception stack of the task that
         * was the last one to run.  If we repeatedly enter the idle loop
         * via reschedule, are interrupted and then reenter the idle loop
         * via reschedule, without dispatching a task, the stack will
         * overflow.  To avoid this, we reset the stack pointer to the
         * base of the exception stack.
         *
         * Before entering the scheduler, we must set kernelState.
         *
         * r0 -> TCB
         */

        /* set kernelState = TRUE */

        MOV     lr, #1
	LDR	r1, L$_kernelState
	STR	lr, [r1]

        /* recover registers from SVC-mode interrupt stack */

	LDMFD	sp!, {r1-r2,r12,lr}

	/*
	 * r0 -> TCB
	 * r1  = svc_sp (original)
	 * r2  = irq_sp/fiq_sp
	 * r12 = svc_r12 (original)
	 * lr  = svc_lr (original)
	 *
	 * SVC-mode interruptStack now flattened
         * Switch SVC-mode stack back to what it was when the IRQ occurred
         */

        LDR     r1, [r0, #WIND_TCB_SP]  /* Get saved SP from the TCB */
        MOV     sp, r1                  /* restore original svc_sp */

#  ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

	/* change to IRQ/FIQ mode to flatten its stack */

	MRS	r1, cpsr			/* save mode and intr state */
        BIC     r3, r1, #MASK_MODE

	LDR	r4, [r2]			/* r4 = irq_cpsr/fiq_cpsr */

	TST	r4, #(MODE_FIQ32 & MASK_SUBMODE)	/* FIQ or IRQ? */
	BNE	set_fiq_mode_1

	ORR	r3, r3, #(MODE_IRQ32 | I_BIT | F_BIT)	/* IRQs/FIQs disabled */
	B	set_mode_1

set_fiq_mode_1:
	ORR	r3, r3, #(MODE_FIQ32 | I_BIT | F_BIT)	/* IRQs/FIQs disabled */

set_mode_1:
	MSR	cpsr, r3

        ADD     sp, sp, #4*8   /* flatten the irq/fiq stack */

        /* back to SVC mode, IRQs/FIQs still disabled */

        ORR     r1, r1, #(I_BIT | F_BIT)        /* interrupts still disabled */
        MSR     cpsr, r1   /* r1 = CPSR */

#  else

	/* change to IRQ mode to flatten its stack */

	MRS	r1, cpsr			/* save mode and intr state */
        BIC     r3, r1, #MASK_MODE
        ORR     r3, r3, #MODE_IRQ32 | I_BIT	/* interrupts still disabled */
        MSR     cpsr, r3
        ADD     sp, sp, #4*7   /* flatten stack */

        /* back to SVC mode, IRQs still disabled */

        ORR     r1, r1, #I_BIT                  /* interrupts still disabled */
        MSR     cpsr, r1   /* r1 = CPSR */

#  endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

        /*
         * For UP, the scheduler runs on current task's exception stack.
         * In this case, r0 is still taskIdCurrent.
         */

        /* reset svc_sp to base of exception stack */

        LDR     r3, [r0, #WIND_TCB_EXC_CNT]       /* get excCnt */

        TEQ     r3, #0                            /* already on exc stack? */
        LDREQ   sp, [r0, #WIND_TCB_P_K_STK_BASE]  /* no? switch to exc stack */

        ADD     r3, r3, #1                        /* increment excCnt */
        STR     r3, [r0, #WIND_TCB_EXC_CNT]       /* and store in TCB */

#  ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

       /* clear intCnt and intNestingLevel */

        LDR	r2, L$_intCnt		/* can't use ADR: offset too large */

        /* r4 = irq_cpsr/fiq_cpsr */

 	TST	r4, #(MODE_FIQ32 & MASK_SUBMODE)	/* FIQ or IRQ? */
	BNE	get_fiq_nestlvl_4

        LDR	r3, L$_intNestingLevel
	B	got_nestlvl_4

get_fiq_nestlvl_4:

        LDR	r3, L$_fiqNestingLevel

got_nestlvl_4:

#  else

       /* clear intCnt and intNestingLevel */

        ADR	r2, L$_intCnt
	LDMIA	r2, {r2,r3}	/* get pointers to intCnt and intNestingLevel */

#  endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

	MOV	lr, #0
	STR	lr, [r2]
	STR	lr, [r3]

  /* now back to SVC mode with interrupts enabled */

#  ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

        BIC     r1, r1, #(I_BIT | F_BIT)

#  else

	BIC	r1, r1, #I_BIT			/* enable interrupts */

#  endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

	MSR	cpsr, r1

	B	FUNC(reschedule)

#endif /* !_WRS_CONFIG_SMP */

/******************************************************************************/

#ifdef _WRS_CONFIG_SV_INSTRUMENTATION

        /* system viewer instrumentation - BEGIN
         * log event if work has been done in the interrupt handler.
         */

instrumentIntExit:

	/*
	 * intExit instrumentation - branched to if currently instrumenting
	 *
	 * event type depends on whether there is anything in the work queue
	 */

	_ARM_PER_CPU_VALUE_GET_SPLR (lr, r0, workQIsEmpty)
	TEQ lr, #0   /* anything in work Q ? */
	MOVNE r0, #EVENT_INT_EXIT /* yes */
	MOVEQ r0, #EVENT_INT_EXIT_K /* no */

	/*
	 * write values to buffer
	 * r0 = event type
         * r1 = NULL (ptr to arg struct)
         * r2 = 0    (sizeof arg struct)
	 */

        MOV     r1, #0
        MOV     r2, #0

        BL FUNC(eventPointSwHandle)

        B  resumeIntExit

        /* system viewer instrumentation - END */
#endif /* _WRS_CONFIG_SV_INSTRUMENTATION */

        FUNC_END(intExit)
        FUNC_END(ARM_intExit)

#ifdef  _WRS_CONFIG_SMP
/*******************************************************************************
*
* intVxdbgCpuRegsGet - VxDBG alternate interrupt exit routine
*
* This routine is an alternate interrupt exit routine that saves the register
* set corresponding to the interrupted context to the per-processor register
* set array (vxdbgCpuCtrlVars[]), and enters into the VxDBG CPU control loop.
*
* This routine is installed by the VxWorks debug library (VxDBG) using
* vxdbgCpuCtrlEnable(), when the debugger needs to stop the OS activity on all
* CPU(s) and retrieve their execution context.
*
* This routine performs the following steps:
*       - Retrieve the pointer to the per-processor register set array for
*         current CPU: &vxdbgCpuCtrlVars[_WRS_CPU_INDEX_GET()]
*       - Copy registers that have already been saved by intEnt() on the
*         IRQ (or FIQ, if supported) stack and on the SVC mode stack.
*       - Save remaining registers (registers not saved on stack by intEnt()).
*       - Enter the VxDBG CPU control loop (Active wait loop) until the CPUs
*         are resumed by the VxWorks debug library.
*       - Jump to the default intExit() routine to exit the interrupt
*         handling using the standard exit path.
*
* This routine is uninstalled from the interrupt handling by the VxWorks debug
* library (VxDBG) using vxdbgCpuCtrlDisable(), when the debugger resumes the
* OS activity on all CPU(s).
*
* The intEnt() routine saves the registers on 2 different stacks.
*
* If unified IRQ/FIQ handling is enabled (#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ),
* the layout of the IRQ or FIQ stack is:
*
* - IRQ/FIQ Stack (Pointed to by <irq_sp/fiq_sp>)
*
*  Values  Offset from <irq_sp/fiq_sp>
*  ------------- --------------------
*  - cpsr  #0
*  - spsr  #4
*  - r0  #8
*  - r1  #12
*  - r2  #16
*  - r3  #20
*  - r4  #24
*  - pc  #28
*
* Otherwise the IRQ layout is:
*
* - IRQ Stack (Pointed to by <irq_sp>)
*
*  Values  Offset from <irq_sp>
*  ------------- --------------------
*  - spsr  #0
*  - r0  #4
*  - r1  #8
*  - r2  #12
*  - r3  #16
*  - r4  #20
*  - pc  #24
*
* The layout of the SVC stack is the same for both cases:
*
* - SVC Interrupt Stack (Current Stack)
*
*  Values  Offset from current sp
*  ------------- ----------------------
*  errno  #0
*  svc_sp  #4
*  irq_sp  #8
*  svc_r12  #12
*  svc_lr  #16
*
* void intVxdbgCpuRegsGet (void)
*
* NOMANUAL
*/

FUNC_BEGIN(intVxdbgCpuRegsGet)

        /* disable IRQs */

        MRS     r1, cpsr

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

        ORR     r1, r1, #(I_BIT | F_BIT)

#else

        ORR     r1, r1, #I_BIT

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

        MSR     cpsr, r1

        /* INTERRUPTS DISABLED */

 /*
  * Retrieve in r0 the pointer to the per-processor register set array
  * for current CPU: &vxdbgCpuCtrlVars[_WRS_CPU_INDEX_GET()]
  */

 _VXDBG_PER_CPU_PREGS_GET(r0, r1) /* Get per cpu REG_SET addr */

 /*
  * First retrieve registers saved on the SVC interrupt stack and copy
  * them to vxdbgCpuCtrlVars[] array:
  *
  * <sp> = SVC interrupt stack
  * <r0> = &vxdbgCpuCtrlVars[_WRS_CPU_INDEX_GET()]
  */

 LDR r1, [sp, #16]    /* retrieve svc_lr */
 STR r1, [r0, #REG_SET_G_REG_OFFSET(14)] /* save r14/lr */

 LDR r1, [sp, #12]    /* retrieve svc_r12 */
 STR r1, [r0, #REG_SET_G_REG_OFFSET(12)] /* save r12 */

 LDR r1, [sp, #8]    /* retrieve irq_sp */
 STR r1, [r0, #REG_SET_G_REG_OFFSET(13)] /* save r13/sp */

 /*
  * Retrieve registers saved on the IRQ stack and copy them
  * to vxdbgCpuCtrlVars[] array:
  *
  * <r1> = IRQ stack
  * <r0> = &vxdbgCpuCtrlVars[_WRS_CPU_INDEX_GET()]
  */

#ifdef _WRS_CONFIG_UNIFIED_FIQ_IRQ

 LDR r2, [r1, #4]    /* retrieve cpsr */
 STR r2, [r0, #REG_SET_CPSR_OFFSET]  /* save cpsr */

 LDR r2, [r1, #8]    /* retrieve r0 */
 STR r2, [r0, #REG_SET_G_REG_OFFSET(0)] /* save r0 */

 LDR r2, [r1, #12]    /* retrieve r1 */
 STR r2, [r0, #REG_SET_G_REG_OFFSET(1)] /* save r1 */

 LDR r2, [r1, #16]    /* retrieve r2 */
 STR r2, [r0, #REG_SET_G_REG_OFFSET(2)] /* save r2 */

 LDR r2, [r1, #20]    /* retrieve r3 */
 STR r2, [r0, #REG_SET_G_REG_OFFSET(3)] /* save r3 */

 LDR r2, [r1, #24]    /* retrieve r4 */
 STR r2, [r0, #REG_SET_G_REG_OFFSET(4)] /* save r4 */

 LDR r2, [r1, #28]    /* retrieve pc */
 STR r2, [r0, #REG_SET_PC_OFFSET]  /* save pc */

#else

 LDR r2, [r1, #0]    /* retrieve cpsr */
 STR r2, [r0, #REG_SET_CPSR_OFFSET]  /* save cpsr */

 LDR r2, [r1, #4]    /* retrieve r0 */
 STR r2, [r0, #REG_SET_G_REG_OFFSET(0)] /* save r0 */

 LDR r2, [r1, #8]    /* retrieve r1 */
 STR r2, [r0, #REG_SET_G_REG_OFFSET(1)] /* save r1 */

 LDR r2, [r1, #12]    /* retrieve r2 */
 STR r2, [r0, #REG_SET_G_REG_OFFSET(2)] /* save r2 */

 LDR r2, [r1, #16]    /* retrieve r3 */
 STR r2, [r0, #REG_SET_G_REG_OFFSET(3)] /* save r3 */

 LDR r2, [r1, #20]    /* retrieve r4 */
 STR r2, [r0, #REG_SET_G_REG_OFFSET(4)] /* save r4 */

 LDR r2, [r1, #24]    /* retrieve pc */
 STR r2, [r0, #REG_SET_PC_OFFSET]  /* save pc */

#endif /* _WRS_CONFIG_UNIFIED_FIQ_IRQ */

 /*
  * Save remaining registers: regs not saved on stack by intEnt():
  *
  * <r0> = &vxdbgCpuCtrlVars[_WRS_CPU_INDEX_GET()]
  */

 STR r5,  [r0, #REG_SET_G_REG_OFFSET(5)] /* save r5 */
 STR r6,  [r0, #REG_SET_G_REG_OFFSET(6)] /* save r6 */
 STR r7,  [r0, #REG_SET_G_REG_OFFSET(7)] /* save r7 */
 STR r8,  [r0, #REG_SET_G_REG_OFFSET(8)] /* save r8 */
 STR r9,  [r0, #REG_SET_G_REG_OFFSET(9)] /* save r9 */
 STR r10, [r0, #REG_SET_G_REG_OFFSET(10)] /* save r10 */
 STR r11, [r0, #REG_SET_G_REG_OFFSET(11)] /* save r11/fp */

 /*
  * Now that all the registers have been saved to the per-processor
  * register set array, we must enter into the VxDBG CPU control loop.
  * This control loop will be exited when the debugger will decide to
  * resume the OS activity on all CPUs.
  */

 BL FUNC(vxdbgArchCpuCtrlLoop)

 /*
  * Exit interrupt handling using the standard interrupt exit routine.
  */

 B ARM_intExit

 FUNC_END(intVxdbgCpuRegsGet)

#endif /* _WRS_CONFIG_SMP */
