/*************************************************
 Copyright (C),  Embed-Te<PERSON> ,TianJin
 File name:       ebtNca9534VxDrv.c.h
 Description:     NCA9534 GPIO Functions  
 
 History:        
 1. Creater: 
    Date:    
    Version:  
    Funtion :
 
 2.  Modification:
     Date:
     Version:  
 
 *************************************************/
/*
 This file contains the configuration parameters and control functions for 
 the NCA9534 GPIO 
 */

#ifndef _NCA9534_GPIO_H
#define _NCA9534_GPIO_H

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

STATUS NCA9534GpioSet (UINT8 unit,UINT8 lineNo,UINT8 value);
STATUS NCA9534GpioGet (UINT8 unit,UINT8 lineNo,UINT8 * gpioData);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* _NCA9534_GPIO_H */

