/*************************************************
  Copyright (C),  Embed-Tec ,TianJin
  File name:      ebtBasisVxDrv.c
  Description:     Basis Functions  

    
*************************************************/
#include <vxWorks.h>
#include "ebtBasisVxDrv.h"
#include "ebtMathVxDrv.h"
#include "vxbFtGpio.h"
#include "ebtEepromDrv.h"



/* debug */
#undef BASIS_DBG_ON
/*#define BASIS_DBG_ON*/
#ifdef BASIS_DBG_ON
#define BASIS_DBG(fmt...)	printf (fmt)
#else
#define BASIS_DBG(fmt...)
#endif	/* BASIS_DBG_ON */

int bsp_slot_get(void)
{
	/*- ��ʼ�����̱��� */

	int value = 0;
	int temp = 0;

	gpioModeSet(2,1,GPIO_MODE_IN);
	gpioModeSet(2,0,GPIO_MODE_IN);
	value=gpioIn(2,1);
	if(value == ERROR)
	{
		BASIS_DBG("bsp_cpu_gpio_get error!\n");
		return ERROR;
	}
	value = value << 1;
	temp=gpioIn(2,0);
	if(temp == ERROR)
	{
		BASIS_DBG("bsp_cpu_gpio_get error!\n");
		return ERROR;
	}

	/*- ����GPIO״̬�������λ�� */
	value |= temp;
	/*- �жϲ�λ�ŵĺϷ��� */
	if((value < 0) || (value > 0x3))
	{
		BASIS_DBG("bsp_slot_get error!\n");
		return ERROR;
	}
	BASIS_DBG("bsp_slot_get:0x%x\n",value);

	return value;

}

int bsp_cpu_id_get(void)
{
	/*- ��ʼ�����̱��� */

	int value = 0;
	int temp = 0;

	gpioModeSet(2,6,GPIO_MODE_IN);
	gpioModeSet(2,5,GPIO_MODE_IN);
	value=gpioIn(2,6);
	if(value == ERROR)
	{
		BASIS_DBG("bsp_cpu_id_get error!\n");
		return ERROR;
	}
	value = value << 1;
	temp=gpioIn(2,5);
	if(temp == ERROR)
	{
		BASIS_DBG("bsp_cpu_id_get error!\n");
		return ERROR;
	}

	/*- GPIO״̬CPUID*/
	value = (value|temp)-1;
	/*-жcpu id ĺϷ */
	if((value < 0) || (value > 2))
	{
		BASIS_DBG("bsp_cpu_id_get error!\n");
		return ERROR;
	}
	printf("cpu_id = 0x%x\n",value);

	return value;
}

UINT32 bsp_hardware_version_get(void)
{
	UINT8 tmp_gpioNo[4]={7,8,9,10};
	UINT32 tmp_gpioData[4] = {0};
	UINT32 tmp_data = 0; 
	int i=0;
	
	/*- ��ȡGPIO��Ϣ��ȡӲ���汾��Ϣ */
	for(i=0;i<4;i++)
	{
		if(gpioModeSet (3,tmp_gpioNo[i], GPIO_MODE_IN)==ERROR)		
		{
			return ERROR;
		}
		tmp_gpioData[i]=gpioIn(3,tmp_gpioNo[i]);
		if(tmp_gpioData[i] == ERROR)
		{
			BASIS_DBG("bsp_cpu_id_get error!\n");
			return ERROR;
		}
		
		tmp_data += (tmp_gpioData[i]<<i);	
	}

	return(tmp_data);
}


LOCAL void bsp_board_info_init(BOARD_INFO *board_info)
{
	int i = 0;
	board_info->cpu_num = MAX_CPU_NUM;
	board_info->led_num = MAX_LED_NUM;
	board_info->gpio_num = MAX_GPIO_NUM;
	board_info->fs_num = MAX_FS_NUM;
	for(i = 0; i < MAX_FS_NUM; i++)
		sprintf(board_info->fs_path[i],"%s:%d",FS_NAME,i);
	board_info->netdev_num = MAX_MOTETSEC_NUM + MAX_GEI_NUM;
	for(i = 0 ; i < MAX_MOTETSEC_NUM ; i++)
	{
		sprintf(board_info->netdev[i].name,"%s%d",MOTETSEC_NAME,i);
		board_info->netdev[i].unit = i;
	}
	for(i = 0 ; i < MAX_GEI_NUM ; i++)
	{
		sprintf(board_info->netdev[i].name,"%s%d",GEI_NAME,i);
		board_info->netdev[i].unit = i;
	}
	board_info->uart_num = MAX_UART_NUM;
	for(i = 0; i < MAX_UART_NUM; i++)
		sprintf(board_info->uart_name[i],"%s/%d",UART_NAME,i);
}
/*************************************************
  Function:       bsp_board_info_get
  Description:    ��ȡboard info
  Input:       BOARD_INFO *board_info
  Output:    ��  
  Return:   
	0:��ȷ
        -1����������
  Others:     �� 
*************************************************/
/**
 * @brief bsp_board_info_get ��ȡ�忨��Ϣ
 *
 * @param *board_info �忨��Ϣ�ṹ�� ȡֵ��Χ: �ǿ�
 * @return  ��������״̬
 */

int32_t bsp_board_info_get(BOARD_INFO *board_info)
{
	/*- �жϲ����Ϸ��� */

	if(board_info == NULL)
	{
		BASIS_DBG("bsp_board_info_get:param is NULL!\n");
		return -1;
	}
	/*- ��ʼ���忨��Ϣ */
	bsp_board_info_init(board_info);
	/*- ��ȷ���� */
	return 0;
}

/******************** Gorgeous Cut Line ********************
 * Function Name:	bsp_board_type_set
 * Description:	32λ������Ϣ����
 * IN:	********
 * OUT:	********
 * RETURN:	OK: 0,
 * 			ERROR: -1
 * Modification:			
 ******************** Gorgeous Cut Line ********************/
 /**
 * @brief bsp_board_type_set ���ð忨����
 *
 * @param  ��
 * @return ��
 */
void bsp_board_type_set(void){

	/*- ��ʼ������ */	
	int i;
	UINT8 temp1, temp2;
	UINT32 val = BOARD_TYPE_NUM;
	UINT32 crc;
	val = val & 0x0000FFFF;
	/*- ��ȡCRCУ��ֵ */	
	crc = crc32(BOARD_CRC, val, BOARD_TYPE_LEN);
	BASIS_DBG("bsp_board_type_set:board_type = 0x%x!\nCRC32 = 0x%x!\n",val,crc);
	/*- ѭ��д��忨����ֵ */	
	for(i = 0;
	 i < BOARD_TYPE_LEN;
	 i++){
	 
		 temp1 = (val >> (8 * i)) & 0xff;
		 temp2 = (crc >> (8 * i)) & 0xff;
		 EEpromWrite(BOARD_TYPE_NUM_OFFSET + i,temp1);
		 taskDelay(5);
		 EEpromWrite(BOARD_TYPE_NUM_CRC_OFFSET + i,temp2);
		 taskDelay(5);
	}
 }

/******************** Gorgeous Cut Line ********************
 * Function Name:	bsp_board_type_set
 * Description:	32λ������Ϣ��ȡ
 * IN:	********
 * OUT:	********
 * RETURN:	OK: BOARD_TYPE_NUM,
 * 			ERROR: 0xffffffff
 * Modification:			
 ******************** Gorgeous Cut Line ********************/
  /**
 * @brief bsp_board_type_get ��ȡ�忨����
 *
 * @param  ��
 * @return �忨����ֵ
 */
UINT32 bsp_board_type_get(void){
	/*- ������̱��� */		
	int i;
	UINT8 temp1, temp2;
	UINT32 val = 0;
	UINT32 crcRead = 0;
	UINT32 crc;
	/*- ѭ����ȡ�忨����ֵ */		
	for(i = 0;
	 i < BOARD_TYPE_LEN;
	 i++){
		temp1 = EEpromRead(BOARD_TYPE_NUM_OFFSET + 3 - i);
		temp2 = EEpromRead(BOARD_TYPE_NUM_CRC_OFFSET + 3 - i);
		val = val << 8;
		crcRead = crcRead << 8;
		val |= temp1;
		crcRead |= temp2;
	}
	/*- ��ȡCRCУ��ֵ */	
	crc = crc32(BOARD_CRC, val, BOARD_TYPE_LEN);
	/*- �ȶ�CRCУ��ֵ */	
	if(crcRead != crc)
	{
		BASIS_DBG("bsp_board_type_get:board type get error:board_type = 0x%x!\nCRC32 = 0x%x!\n",val,crcRead);
		return 0xffffffffU;
	}
	BASIS_DBG("bsp_board_type_get:board_type = 0x%x!\nCRC32 = 0x%x!\n",val,crc);
	/*- ���ذ忨����ֵ */	
	return val;
 }

/*************************************************
  Function:        bsp_software_version_get
  Description:    bsp�汾�Ż�ȡ
  Input:       ��
  Output:    ��
  Return:   
		BSP�汾��
  Others:     �� 
*************************************************/
UINT32  bsp_software_version_get(void)
{
	/*-����BSP�汾��*/
	return BSP_REV_NUM;
}


/*CPU3 ��λ���� 
���� :
channel:  led ���(0-3)
val:     0 on��1 off
*/
STATUS cpu_led_set(UINT8 channel,UINT32 val)
{
	STATUS rval = OK;

	switch (channel)
	{
		case 0:
			
			gpioModeSet(5,8,GPIO_MODE_OUT);
			rval=gpioOut(5,8,val);
			if(rval ==ERROR)
			{
				BASIS_DBG("cpu_led_set error!\n");
			}
			break;
		case 1:
			
			gpioModeSet(5,9,GPIO_MODE_OUT);
			rval=gpioOut(5,9,val);
			if(rval ==ERROR)
			{
				BASIS_DBG("cpu_led_set error!\n");
			}
			break;
		case 2:
			
			gpioModeSet(5,10,GPIO_MODE_OUT);
			rval=gpioOut(5,10,val);
			if(rval ==ERROR)
			{
				BASIS_DBG("cpu_led_set error!\n");
			}
			break;
		case 3:
			
			gpioModeSet(5,11,GPIO_MODE_OUT);
			rval=gpioOut(5,11,val);
			if(rval ==ERROR)
			{
				BASIS_DBG("cpu_led_set error!\n");
			}
			break;
		default: 
			rval=ERROR;
			break;
	}
	return rval;
}




/*�����Դ �ز�:  1 close    0 open */
int output_pw_back(void)
{
	int value = 0;

	gpioModeSet(1,4,GPIO_MODE_IN);
	
	value=gpioIn(1,4);
	if(value == ERROR)
	{
		return ERROR;
	}
	return value;
}


/*CPU ���ĵ�ѹ���*/
int core_voltage_mon(void)
{
	int value = 0;

	gpioModeSet(2,2,GPIO_MODE_IN);
	
	value=gpioIn(2,2);
	if(value == ERROR)
	{
		return ERROR;
	}
	return value;
}


/*5V ϵͳ��ѹ���*/

int voltage_5v_mon(void)
{
	int value = 0;

	gpioModeSet(2,7,GPIO_MODE_IN);
	
	value=gpioIn(2,7);
	if(value == ERROR)
	{
		return ERROR;
	}
	return value;
}


STATUS cpu_connect_init(void)
{
	char ret = 0;
	
	ret=gpioModeSet(0,0,GPIO_MODE_OUT);

	if(ret ==ERROR)
	{
		BASIS_DBG("cpu_connect_init error!\n");
		return ERROR;
	}
	gpioModeSet(0,12,GPIO_MODE_OUT);
	gpioModeSet(0,13,GPIO_MODE_IN);
	gpioModeSet(0,14,GPIO_MODE_IN);
	return OK;
	
}

/* Function to test GPIO port 2 availability */
STATUS testGpioPort2(void)
{
    VXB_DEVICE_ID pDev;
    int i;
    
    printf("Testing GPIO port 2 driver initialization...\n");
    
    /* Try to find the GPIO device */
    pDev = vxbInstByNameFind("ftGpio", 2);
    if(pDev == NULL)
    {
        printf("ERROR: GPIO port 2 device not found!\n");
        return ERROR;
    }
    
    printf("GPIO port 2 device found. Testing pins...\n");
    
    /* Test setting mode on a few pins */
    for(i = 0; i < 8; i++)
    {
        if(gpioModeSet(2, i, GPIO_MODE_IN) == ERROR)
        {
            printf("ERROR: Failed to set GPIO port 2, pin %d mode\n", i);
            return ERROR;
        }
        printf("Successfully set GPIO port 2, pin %d mode\n", i);
    }
    
    printf("GPIO port 2 pin modes set successfully\n");
    return OK;
}



