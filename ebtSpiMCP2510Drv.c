/*******************************************************************************\
  Copyright (C),  Embed-Tec ,TianJin
  File name:     vxbSpiMCP2510.c
   Author:  wanglili     Version:  V1.0      Date: 26may2016
  Description:  Spi CAN  for ht5188
  
   History:        
  1. Date:
     Author:
     Modification: 
\*******************************************************************************/

#include <vxWorks.h>
#include <stdio.h>			/* for printf() */
#include <logLib.h>			/* for logMsg() */
#include <string.h>			/* for strcmp() */
#include <intLib.h>

#include <vxBusLib.h>			/* for VXB_DEVICE_ID */
#include <hwif/vxbus/vxBus.h>
#include <hwif/util/hwMemLib.h>		/* for hwMemAlloc() */
#include <hwif/vxbus/hwConf.h>		/* for devResourceGet() and
					 * hcfDeviceGet()
					 */
#include <hwif/util/vxbParamSys.h>
#include <hwif/vxbus/vxbSpiLib.h>
#include <driverControl.h>		/* for struct vxbDriverControl */

#include "ebtSpiMCP2510Drv.h"
#include <vxbFtGpio.h>


#include "sysLib.h"

#include "semLib.h"
#include "msgQLib.h"
#include "iv.h"
#include "eventLib.h"  


static MCP2510_DEV  MCP2510Device[N_MCP2510_CHANNELS]={0};

#if 0
LOCAL MSG_Q_ID	can_msgQId;
LOCAL SEM_ID 	can_semTx;
LOCAL SEM_ID 	sem_isr;
#endif

int canIsrCountRcv0 = 0, canIsrCountRcv1 = 0, canIsrCountSend= 0, countCan = 0;    /*20250219 test can ISR*/

CANInitInBuf CANCFG;

LOCAL   UCHAR  intType = 0;
LOCAL volatile UCHAR rcvbuf[50];
LOCAL volatile UCHAR sendbuf[50];
LOCAL volatile int rcvbuflen;


#define DEBUG_RXANY		0
#define  NUM_MSGS         				1000
#define MCP_N_TXBUFFERS 			3
#define CAN_MAX_CHAR_IN_MESSAGE 	24



/* Name of the driver. This name shall be used in hwconf.c file. */
#define DRIVER_NAME_MCP2510 "MCP2510"

/* Maximum length of CAN frame */
#define MCP2510_CAN_MAX_DLC   (8)

#define MCP2510_TX_BUFFER_COUNT     (3)
#define MCP2510_RX_BUFFER_COUNT     (2)

/* Number of internal registers */
#define MCP2510_REGISTER_COUNT   (128)


/* ============================  MCP2510 CAN SPI commands ============================ */

/* SPI command to reset device */
#define MCP2510_CMD_RESET (0xC0)


/* SPI command to write internal registers */
#define MCP2510_CMD_WRITE_REGISTER  (0x02)

/* SPI command to read internal registers */
#define MCP2510_CMD_READ_REGISTER (0x03)

/* SPI command to write transmit buffers.
 * NOTE: the 3 least significant bytes identifies transmit buffer - it has to be adjusted.
 */
#define MCP2510_CMD_LOADTXB   (0x40)

/*SPI command to read status*/
#define MCP2510_CMD_STATUS	  (0xA0)

/* SPI command to reset controller */
#define MCP2510_CMD_RESET     (0xC0)

/* SPI command to request transmission on specified buffer.
 * NOTE: the 3 least significant bytes identifies transmit buffer.
 */
#define MCP2510_CMD_RTS(buffer)     (0x80 | ((1 << (buffer)) & 0x07))

/* ----------------------------  MCP2510 CAN SPI command lengths ---------------------------- */

/* Length of SPI command to read internal registers */
#define MCP2510_CMD_READ_REGISTER_LEN (2)

/* Length of SPI sequence writing to internal registers */
#define MCP2510_CMD_WRITE_REGISTER_LEN   (3)

/* Length of SPI sequence writing to TX buffers */
#define MCP2510_CMD_LOADTXB_LEN     (6 + MCP2510_CAN_MAX_DLC)




/* ============================  MCP2510 CAN internal registers ============================ */
#define MCP2510_CANCTRL    (0x0F)

#define MCP2510_CNF1       (0x2A)
#define MCP2510_CNF2       (0x29)
#define MCP2510_CNF3       (0x28)

/* Receive control registers */
#define MCP2510_RXB0CTRL       (0x60)
#define MCP2510_RXB1CTRL       (0x70)

/* Interrupt enable register */
#define MCP2510_CANINTE        (0x2B)

#define MCP2510_CNF1_SJW_SHIFT   (6)

/*  BTLMODE mask in CNF2 register */
#define MCP2510_CNF2_BTLMODE     (0x80)

/* Position of PS1 bits in CNF2 register */
#define MCP2510_CNF2_PS1_SHIFT   (3)

/* The delay after each SPI transmission [us]. */
#define MCP2510_SPI_DELAY_US  (0)


/* debug */
#undef VXBMCP2510_DBG_ON

#ifdef VXBMCP2510_DBG_ON

/*
 * NOTE: printf() and logMsg() debugging cannot be used before the
 * call to vxbMCP2510_InstConnect().  Any use before that may cause the
 * system to crash before finishing the boot process.  To debug
 * the probe routine, init routine, and init2 routine, either
 * use a hardware debugger, move the specified functionality to
 * vxbMCP2510_InstConnect(), or manually call the driver registration
 * routine after boot.
 */

#define VXBMCP2510_DBG_INFO           (0x00000001)
#define VXBMCP2510_DBG_WARN           (0x00000002)
#define VXBMCP2510_DBG_ERR            (0x00000004)

#define VXBMCP2510_DBG_ALL            (0xffffffff)
#define VXBMCP2510_DBG_OFF            (0x00000000)

int vxbMCP2510_DebugLevel = VXBMCP2510_DBG_ALL;

#define VXBSPIMCP2510_DBG_MSG(mask,fmt,a,b,c,d,e,f)	\
    if ((vxbMCP2510_DebugLevel & (mask)) != 0)		\
	logMsg (fmt, (_Vx_usr_arg_t) a,		\
		     (_Vx_usr_arg_t) b,		\
		     (_Vx_usr_arg_t) c,		\
		     (_Vx_usr_arg_t) d,		\
		     (_Vx_usr_arg_t) e,		\
		     (_Vx_usr_arg_t) f)
		     
#undef LOCAL
#define LOCAL
#else /* vxbMCP2510__DBG_ON */
#define VXBSPIMCP2510_DBG_MSG(level,fmt,a,b,c,d,e,f)
#endif /* vxbMCP2510__DBG_ON */


#undef MCP2510_DBG_ON /* debug */

#ifdef MCP2510_DBG_ON
#define MCP2510_DBG(fmt...)	printf (fmt)
#else
#define MCP2510_DBG(fmt...)
#endif
/* typedefs */

/* Device specific context structure (pointed to by pDrvCtrl in the vxbDev structure) */
struct vxbSpiMCP2510_pDrvCtrl
{
      int functionality;
};

/* Settings to get 125kbps */
LOCAL const vxbSpiMCP2510_CanTimingType vxbSpiMCP2510_CanTiming_125k =
{
    8,       /* baud rate prescaler */
    1,       /* SJW */
    1,       /* prop seg (range: 1 - 8) */
    3,       /* Tseg1 (range: 1 - 8) */
    3,       /* Tseg2 (range: 2 - 8) */
    0        /* sample 3 times per bit (if TRUE), otherwise - sample only once */
};

/* Settings to get 1Mbps */
LOCAL const vxbSpiMCP2510_CanTimingType vxbSpiMCP2510_CanTiming_1M =
{
    1,       /* baud rate prescaler */
    1,       /* SJW */
    1,       /* prop seg (range: 1 - 8) */
    3,       /* Tseg1 (range: 1 - 8) */
    3,       /* Tseg2 (range: 2 - 8) */
    0        /* sample 3 times per bit (if TRUE), otherwise - sample only once */
};

/* locals */

LOCAL void vxbSpiMCP2510_InstInit(VXB_DEVICE_ID pInst);
LOCAL void vxbSpiMCP2510_InstInit2(VXB_DEVICE_ID pInst);
LOCAL void vxbSpiMCP2510_InstConnect(VXB_DEVICE_ID pInst);


LOCAL STATUS vxbSpiMCP2510_SetTiming
   (
      VXB_DEVICE_ID pInst,
      const vxbSpiMCP2510_CanTimingType * timing_params
   );

LOCAL STATUS vxbSpiMCP2510_SetMode
   (
      VXB_DEVICE_ID pInst,
      UINT8 mode
   );

LOCAL STATUS vxbSpiMCP2510_Init
   (
      VXB_DEVICE_ID pInst
   );

LOCAL STATUS vxbSpiMCP2510_Reset
   (
      VXB_DEVICE_ID pInst
   );

LOCAL struct drvBusFuncs vxbMCP2510_Funcs =
    {
    vxbSpiMCP2510_InstInit,        /* devInstanceInit */
    vxbSpiMCP2510_InstInit2,       /* devInstanceInit2 */
    vxbSpiMCP2510_InstConnect      /* devConnect */
    };
LOCAL UINT8 mcp2510_getNextFreeTXBuf(VXB_DEVICE_ID pInst, UINT8 *txbuf_n);
LOCAL STATUS mcp2510_setRegisterS(VXB_DEVICE_ID pInst, const UINT8 address, const UINT8 values[], const UINT8 n);
LOCAL STATUS mcp2510_setRegister(VXB_DEVICE_ID pInst, const UINT8 address, const UINT8 value);
LOCAL void mcp2510_write_can_id(VXB_DEVICE_ID pInst, const UINT8 mcp_addr, const UINT8 ext,  const UINT32 can_id );
LOCAL void mcp2510_write_canMsg(VXB_DEVICE_ID pInst, const UINT8 addr, const CANMSG* msg);
LOCAL void mcp2510_start_transmit(VXB_DEVICE_ID pInst, const UINT8 buffer_sidh_addr);
LOCAL void mcp2510_ISR_t0(MCP2510_DEV *pDev);

LOCAL UINT8 mcp2510ReadRxID(VXB_DEVICE_ID pInst, UINT8 addr, UINT8 *ext, long *canid, UINT8 *rtr);
LOCAL void mcp2510ReadRxBuf(VXB_DEVICE_ID pInst, UINT8 addr, UINT8 *rbuf);
LOCAL void mcp2510ReadRx(VXB_DEVICE_ID pInst, UINT8 ridcmd,UINT8 rbufcmd, CANMSG *msg);
LOCAL void mcp2510_ISR(MCP2510_DEV *pDev);
LOCAL void mcp2510_ISR_IRQx(MCP2510_DEV *pDev);

LOCAL void sys_mcp2510Int_config(MCP2510_DEV *pDev);
LOCAL STATUS mcp2510_configRate(VXB_DEVICE_ID pInst, unsigned int canbaud);
LOCAL void mcp2510_initCANBuffers(VXB_DEVICE_ID pInst);
LOCAL void mcp2510SetRxMaskFilter(VXB_DEVICE_ID pInst, const UINT8 addr, long mask, UINT8 ext);
LOCAL STATUS  mcp2510_init (VXB_DEVICE_ID pInst,long canmask,long canid,unsigned int canbaud,UINT8 ext);
LOCAL void mcp2510_initINT(VXB_DEVICE_ID pInst) ;
LOCAL STATUS canRcv(MCP2510_DEV *pDev, CANMSG *RcvMsg);
LOCAL STATUS canSend(MCP2510_DEV *pDev,CANMSG msg);
void can_testTransmit(MCP2510_DEV *pDev);
void can_testReceive(MCP2510_DEV *pDev);
STATUS WWCan_Reset(void);

STATUS mcp2510_setCANCTRL_Mode
(
	VXB_DEVICE_ID pInst, 
	const UINT8 newmode
);

STATUS canConfig
(
	MCP2510_DEV *pDev,
	long canmask,
	long canid,
	unsigned int canbaud,
	UCHAR ext
);

STATUS vxbSpiMCP2510_TxMsg_1
(
      VXB_DEVICE_ID pInst,
      CANMSG* msg
);



/*
 * This sample represents a custom driver class.  Normal VxBus
 * driver methods are not available, but we can use the generic
 * {driverControl}() method to accomplish what we need to.
 * Therefore, we publish only that method.
 *
 * Note that the structures used by the {driverControl}() method
 * must be defined in a per-driver or per-application header file,
 * in addition to the driverControl.h header file.  In this case,
 * the header file is target/3rdparty/windriver/sample/wrsample.h
 * and applications are responsible for ensuring that they include
 * that file when they need to interact with the instance.
 */

LOCAL device_method_t vxbSpiMCP2510_methods[] =
    {
    DEVMETHOD_END
    };


LOCAL struct vxbDevRegInfo vxbSpiMCP2510_DevRegistration =
    {
    NULL,               /* pNext */
    VXB_DEVID_DEVICE,   /* devID */
    VXB_BUSID_SPI,      /* busID = SPI */
    VXB_VER_5_0_0,      /* vxbVersion */
    DRIVER_NAME_MCP2510,         /* drvName */
    &vxbMCP2510_Funcs,     /* pDrvBusFuncs */
    NULL,               /* pMethods */
    NULL,               /* devProbe */
    NULL                /* pParamDefaults */
    };



/* ------------------------------------------------------------ */

/* local defines       */

/* driver functions */

LOCAL STATUS vxbSpiMCP2510_WriteRegister
   (
      VXB_DEVICE_ID pInst,
      UINT8 address,
      UINT8 value
   );


/*******************************************************************************
*
* vxbMCP2510_Register - register vxbMCP2510 driver
*
* This routine registers the vxbMCP2510 driver and device recognition
* data with the vxBus subsystem.
*
* RETURNS: N/A
*
* ERRNO
*/

void vxbSpiMCP2510_Register(void)
    {
    vxbDevRegister((struct vxbDevRegInfo *)&vxbSpiMCP2510_DevRegistration);
    }

/*******************************************************************************
*
* vxbMCP2510_InstInit - initialize vxbMCP2510 device
*
* This is the vxbMCP2510 initialization routine.
*
* RETURNS: N/A
*
* ERRNO
*/

LOCAL void vxbSpiMCP2510_InstInit
    (
    VXB_DEVICE_ID pInst
    )
    {
    struct vxbSpiMCP2510_pDrvCtrl * pDrvCtrl;


    /* check for vaild parameter */

    if (pInst == NULL)
        return;

    /* allocate the memory for the structure */

    pDrvCtrl = (struct vxbSpiMCP2510_pDrvCtrl *)
		hwMemAlloc (sizeof (struct vxbSpiMCP2510_pDrvCtrl));

    /* check if memory allocation is successful */

    if (pDrvCtrl == NULL)
        return;

    /* publish methods */

    pInst->pMethods = &vxbSpiMCP2510_methods[0];


    /* Install the callback function */

    /*pDrvCtrl->functionality.GetTemperature = &vxbSpiMCP2510_GetTemperature;*/

    /* per-device init */

    pInst->pDrvCtrl = pDrvCtrl;

    /* Always use the unit number allocated to us in the hwconf file. */
    vxbNextUnitGet (pInst);
    }

/*******************************************************************************
*
* vxbMCP2510_InstInit2 - initialize vxbMCP2510 device
*
* This is seconde phase initialize routine for VxBus driver.
*
* RETURNS: N/A
*
* ERRNO
*/

LOCAL void vxbSpiMCP2510_InstInit2
    (
    VXB_DEVICE_ID pInst
    )
    {


    }

/*******************************************************************************
*
* vxbMCP2510_InstConnect - VxBus connect phase routine for vxbMCP2510 driver
*
* This is connect phase routine.
*
* RETURNS: N/A
*
* ERRNO: not set
*/

LOCAL void vxbSpiMCP2510_InstConnect
    (
    VXB_DEVICE_ID pInst
    )
{
    VXBSPIMCP2510_DBG_MSG(VXBMCP2510_DBG_INFO,"vxbSpiMCP2510_InstConnect() called\n", 1,2,3,4,5,6);

#ifdef LZE_UNDO
    
    // reset device, adapt to beta board reset MUX, or - skip resetting?

    /* Bring out from reset state - our BSP only drives RESET line into active state. */
    //sysGpioLineSet (GPIO_13, GPIO_HIGH);
#endif

   /* vxbSpiMCP2510_Init(pInst);*/
}

LOCAL STATUS vxbSpiMCP2510_SetupRxBuffers
   (
      VXB_DEVICE_ID pInst
   )
{
   /* Configure receive buffers */
   vxbSpiMCP2510_WriteRegister(pInst, MCP2510_RXB0CTRL, 0x64);

   vxbSpiMCP2510_WriteRegister(pInst, MCP2510_RXB0CTRL, 0x60);

   /* Enable RX interrupts inside MCP2510 */
   vxbSpiMCP2510_WriteRegister(pInst, MCP2510_CANINTE, 0x03);

   return OK;
}

LOCAL STATUS vxbSpiMCP2510_Init
   (
      VXB_DEVICE_ID pInst
   )
{

   /* add checking return values*/
	
   vxbSpiMCP2510_Reset(pInst);

   /* Initialize timing, baudrate registers */
   vxbSpiMCP2510_SetTiming(pInst, &vxbSpiMCP2510_CanTiming_1M);

   vxbSpiMCP2510_SetupRxBuffers(pInst);

   vxbSpiMCP2510_SetMode(pInst, 0);

   return OK;
}

LOCAL STATUS vxbSpiMCP2510_SetTiming
   (
      VXB_DEVICE_ID pInst,
      const vxbSpiMCP2510_CanTimingType * timing_params
   )
{
   UINT8 reg_val;

   /* potential optimization: we write to addresses 0x28, 0x29, 0x2A - it could be done in single SPI transfer*/

   /* CNF1: SJW, prescaler */
   reg_val = ((timing_params->sjw - 1) << MCP2510_CNF1_SJW_SHIFT) | (timing_params->brp - 1);
   vxbSpiMCP2510_WriteRegister(pInst, MCP2510_CNF1, reg_val);

   /* number of samples per bit shall be configurable*/
   /* CNF2:
    * - BTLMODE is set, so length of PS2 determined by PHSEG22:PHSEG20 bits of CNF3
    * - by default we sample once per bit
    * - TSEG1
    * - propagation segment
    */
   reg_val = MCP2510_CNF2_BTLMODE | ((timing_params->tseg1 - 1) << MCP2510_CNF2_PS1_SHIFT) | (timing_params->prop_seg - 1);
   vxbSpiMCP2510_WriteRegister(pInst, MCP2510_CNF2, reg_val);

   /* CNF3:
    * - TSEG2
    */
   reg_val = timing_params->tseg2 - 1;
   vxbSpiMCP2510_WriteRegister(pInst, MCP2510_CNF3, reg_val);

   return OK;
}




/* Send Request to Send */
LOCAL STATUS vxbSpiMCP2510_RequestToSend
   (
      VXB_DEVICE_ID pInst,
      UINT8 channel
   )
{
   SPI_TRANSFER spi_params;
   UINT8 rts_cmd;

   /*check of channel parameter (1-3)*/

   rts_cmd = MCP2510_CMD_RTS(channel);

   spi_params.rxBuf = NULL;
   spi_params.txBuf = &rts_cmd;
   spi_params.rxLen = 0;
   spi_params.txLen = 1;
   spi_params.usDelay = MCP2510_SPI_DELAY_US;


   if (vxbSpiTransfer (pInst, &spi_params) != OK)
   {
      VXBSPIMCP2510_DBG_MSG(VXBMCP2510_DBG_ERR,"MCP2510: Writing RTS for buffer %x failed.\n", channel,2,3,4,5,6);
      return ERROR;
   }

   return OK;
}


LOCAL STATUS vxbSpiMCP2510_Reset
   (
      VXB_DEVICE_ID pInst
   )
{
	SPI_TRANSFER spi_params;
	const UINT8 reset_cmd = MCP2510_CMD_RESET;

   	spi_params.rxBuf = NULL;
   	spi_params.txBuf = (UINT8 *) &reset_cmd;
   	spi_params.rxLen = 0;
   	spi_params.txLen = 1;
   	spi_params.usDelay = MCP2510_SPI_DELAY_US;


  	if (vxbSpiTransfer (pInst, &spi_params) != OK)
   	{
		VXBSPIMCP2510_DBG_MSG(VXBMCP2510_DBG_ERR,"MCP2510: Sending SPI RESET command failed.\n", 1,2,3,4,5,6);
		return ERROR;
   	}
	taskDelay(1); 

   	return OK;
}

/*2020-01-18 add can status*/
LOCAL STATUS vxbSpiMCP2510_Status(VXB_DEVICE_ID pInst,UINT8 *status_value)
{
	SPI_TRANSFER spi_params;
	const UINT8 status_cmd = MCP2510_CMD_STATUS;

	spi_params.rxBuf = status_value;
	spi_params.txBuf = (UINT8 *) &status_cmd;
	spi_params.rxLen = 2;
	spi_params.txLen = 1;
	spi_params.usDelay = MCP2510_SPI_DELAY_US;
	
	if (vxbSpiTransfer (pInst, &spi_params) != OK)
	{
		VXBSPIMCP2510_DBG_MSG(VXBMCP2510_DBG_ERR,"MCP2510: Sending SPI STATUS command failed.\n", 1,2,3,4,5,6);
		return ERROR;
	}
	MCP2510_DBG("CAN Status is 0x%x \n",*status_value);
	taskDelay(1);
	
	return OK;
}

LOCAL STATUS vxbSpiMCP2510_WriteRegister
	(
      VXB_DEVICE_ID pInst,
      UINT8 address,
      UINT8 value
	)
{
   SPI_TRANSFER spi_params;
   UINT8 buffer_tx[MCP2510_CMD_WRITE_REGISTER_LEN];

   spi_params.rxBuf = NULL;
   spi_params.txBuf = buffer_tx;
   spi_params.rxLen = 0;
   spi_params.txLen = MCP2510_CMD_WRITE_REGISTER_LEN;
   spi_params.usDelay = MCP2510_SPI_DELAY_US;

   buffer_tx[0] = MCP2510_CMD_WRITE_REGISTER;
   buffer_tx[1] = address;
   buffer_tx[2] = value;

   if (vxbSpiTransfer (pInst, &spi_params) != OK)
   {
      VXBSPIMCP2510_DBG_MSG(VXBMCP2510_DBG_ERR,"MCP2510: Writing register %x failed.", address,2,3,4,5,6);
      return ERROR;
   }

   return OK;
}

LOCAL STATUS vxbSpiMCP2510_ReadRegister
   (
      VXB_DEVICE_ID pInst,
      UINT8 address,
      UINT8 * register_value
   )
{
   SPI_TRANSFER spi_params;
   UINT8 buffer_tx[2];

   spi_params.rxBuf = register_value;
   spi_params.txBuf = buffer_tx;
   spi_params.rxLen = 1;
   spi_params.txLen = 2;
   spi_params.usDelay = MCP2510_SPI_DELAY_US;

   buffer_tx[0] = MCP2510_CMD_READ_REGISTER;
   buffer_tx[1] = address;

   if (vxbSpiTransfer (pInst, &spi_params) != OK)
   {
	   VXBSPIMCP2510_DBG_MSG(VXBMCP2510_DBG_ERR,"MCP2510: Reading register %#x failed.", address,2,3,4,5,6);
      return ERROR;
   }
   #if 0
   else
   {
     VXBSPIMCP2510_DBG_MSG(VXBMCP2510_DBG_ERR,"MCP2510: Reading register %#x ok.", address,2,3,4,5,6);
   }
  #endif 

   return OK;
}

LOCAL STATUS vxbSpiMCP2510_ReadNRegisters
   (
      VXB_DEVICE_ID pInst,
      UINT8 address,
      UINT8 * buffer,
      UINT8 register_count
   )
{
   SPI_TRANSFER spi_params;
   UINT8 buffer_tx[2];

   spi_params.rxBuf = buffer;
   spi_params.txBuf = buffer_tx;
   spi_params.rxLen = register_count;
   spi_params.txLen = 2;
   spi_params.usDelay = MCP2510_SPI_DELAY_US;

   buffer_tx[0] = MCP2510_CMD_READ_REGISTER;
   buffer_tx[1] = address;

   if (vxbSpiTransfer (pInst, &spi_params) != OK)
   {
	   VXBSPIMCP2510_DBG_MSG(VXBMCP2510_DBG_ERR,"MCP2510: Reading N registers from address %x failed.", address,2,3,4,5,6);
      return ERROR;
   }

   return OK;
}

LOCAL STATUS vxbSpiMCP2510_SetMode
   (
      VXB_DEVICE_ID pInst,
      UINT8 mode
   )
{
   /*  support other modes, currently set only to normal mode*/
   return vxbSpiMCP2510_WriteRegister(pInst, MCP2510_CANCTRL, 0x00);
}

LOCAL STATUS vxbSpiMCP2510_ShowRegisters
   (
      VXB_DEVICE_ID pInst,
      UINT8 address,
      UINT8 count
   )
{
	UINT8 buffer[MCP2510_REGISTER_COUNT];
   	UINT8 reg_idx;

   
 	memset(buffer, 0, MCP2510_REGISTER_COUNT);

   	if (vxbSpiMCP2510_ReadNRegisters(pInst, address, buffer, count) == OK)
   	{
      		for (reg_idx = 0; reg_idx < count; reg_idx++)
      		{
         		printf ("Address: 0x%02X value: 0x%02X\n", address, buffer[reg_idx]);
         		
         		address++;
      		}
   	}
   	
   return OK;
}


STATUS vxbSpiMCP2510_TxMsg
(
      VXB_DEVICE_ID pInst,
      UINT8 channel,
      UINT32 can_id,
      BOOL extended_id,
      const UINT8 * data,
      UINT8  length
)
{
   UINT8 buffer[MCP2510_CMD_LOADTXB_LEN];

   /* now all input parameters are ignored*/
   /*  check length*/

   buffer[0] = MCP2510_CMD_LOADTXB;    /* now we use only first buffer */
   buffer[1] = 0x00;    /* standard identifier - high byte */
   buffer[2] = 0x60;    /* standard identifier - low byte, EXIDE bit cleared */
   buffer[3] = 0x00;    /* extended identifier - high byte */
   buffer[4] = 0x00;    /* extended identifier - low byte */
   buffer[5] = length;

   memcpy(&buffer[6], data, length);

   /* encapsulate following in some function*/
   SPI_TRANSFER spi_params;


      spi_params.rxBuf = NULL;
      spi_params.txBuf = buffer;
      spi_params.rxLen = 0;
      spi_params.txLen = 6 + length;
      spi_params.usDelay = MCP2510_SPI_DELAY_US;

      if (vxbSpiTransfer (pInst, &spi_params) != OK)
      {
         printf ("MCP2510: Writing TX setup sequence failed.\n");
         return ERROR;
      }

   /* Previous operations only put data into buffers. To request frame transmision, the RTS command has to be sent. */
   vxbSpiMCP2510_RequestToSend(pInst, channel);

   return OK;
}


/*---------only use send buffer 0----------*/
LOCAL UINT8 mcp2510_getNextFreeTXBuf(VXB_DEVICE_ID pInst, UINT8 *txbuf_n)
{
	UINT8 res, i, ctrlval;
	#if 1
	UINT8 ctrlregs[MCP_N_TXBUFFERS] = { MCP_TXB0CTRL, MCP_TXB1CTRL, MCP_TXB2CTRL };
	#else
	UINT8 ctrlregs[1] = { MCP_TXB0CTRL };

	#endif
	res = MCP_ALLTXBUSY;
	*txbuf_n = 0x00;
	
	/* check all 3 TX-Buffers*/
	for (i=0; i<3; i++) 
	{
		if(( vxbSpiMCP2510_ReadNRegisters(pInst, ctrlregs[i], &ctrlval,1))==OK)
		{
			if ( (ctrlval & MCP_TXB_TXREQ_M) == 0 ) 
			{
				*txbuf_n = ctrlregs[i]+1; /* return SIDH-address of Buffer*/
				res = MCP2510_OK;
				return res; /* ! function exit */
			}
		}	

	}
	
	return res;
	
}


LOCAL STATUS mcp2510_setRegisterS(VXB_DEVICE_ID pInst, const UINT8 address, 
	const UINT8 values[], const UINT8 n)
{
	UINT8 cmd[20];
  	SPI_TRANSFER spi_params;	
	cmd[0] = MCP_WRITE;
	cmd[1] = address;
	/*for(i=0;i<n;i++)
		{
			cmd[i+2] = values[i];
		}*/
	memcpy(cmd+2,values,n);

   	spi_params.rxBuf = NULL;
   	spi_params.txBuf = cmd;
   	spi_params.rxLen = 0;
   	spi_params.txLen = n+2;
   	spi_params.usDelay = MCP2510_SPI_DELAY_US;

   	if (vxbSpiTransfer (pInst, &spi_params) != OK)
   	{
      		VXBSPIMCP2510_DBG_MSG(VXBMCP2510_DBG_ERR,"MCP2510: Writing register %x failed.", address,2,3,4,5,6);
      		return ERROR;
   	}
   	return OK;
}


LOCAL STATUS mcp2510_setRegister(VXB_DEVICE_ID pInst, const UINT8 address, const UINT8 value)
{	
	UINT8 cmd[3];
  	SPI_TRANSFER spi_params;		
	cmd[0] = MCP_WRITE;
	cmd[1] = address;
	cmd[2] = value;

   	spi_params.rxBuf = NULL;
   	spi_params.txBuf = cmd;
   	spi_params.rxLen = 0;
   	spi_params.txLen = 3;
   	spi_params.usDelay = MCP2510_SPI_DELAY_US;

   	if (vxbSpiTransfer (pInst, &spi_params) != OK)
   	{
      		VXBSPIMCP2510_DBG_MSG(VXBMCP2510_DBG_ERR,"MCP2510: Writing register %x failed.", address,2,3,4,5,6);
      		return ERROR;
   	}

   	return OK;



}



LOCAL void mcp2510_write_can_id(VXB_DEVICE_ID pInst, const UINT8 mcp_addr, const UINT8 ext,  const UINT32 can_id )
{
    UINT16 canid;
    UINT8 tbufdata[4];
	
    canid = (UINT16)(can_id & 0x0FFFF);
    
	if ( ext == 1) 
	{
        	tbufdata[MCP_EID0] = (UINT8) (canid & 0xFF);
        	tbufdata[MCP_EID8] = (UINT8) (canid / 256);
        	canid = (UINT16)( can_id / 0x10000L );
        	tbufdata[MCP_SIDL] = (UINT8) (canid & 0x03);
        	tbufdata[MCP_SIDL] += (UINT8) ((canid & 0x1C )*8);
        	tbufdata[MCP_SIDL] |= MCP_TXB_EXIDE_M;
        	tbufdata[MCP_SIDH] = (UINT8) (canid / 32 );
    	}
    else 
	{
        	tbufdata[MCP_SIDH] = (UINT8) (canid / 8 );
        	tbufdata[MCP_SIDL] = (UINT8) ((canid & 0x07 )*32);
        	tbufdata[MCP_EID0] = 0;
        	tbufdata[MCP_EID8] = 0;
    }

	mcp2510_setRegisterS(pInst, mcp_addr, tbufdata, 4 );
	
}







LOCAL void mcp2510_write_canMsg(VXB_DEVICE_ID pInst, const UINT8 addr, const CANMSG* msg)
{
    	UINT8 mcp_addr, dlc;
    	dlc = msg->canLength;
    	mcp_addr = addr;
	
    	mcp2510_setRegisterS(pInst, mcp_addr+5, &(msg->cbuf[0]), dlc );  /* write data bytes*/
	
    	mcp2510_write_can_id(pInst, mcp_addr, msg->frameFormat, msg->canID);  /* write CAN id8*/
		
    	if ( msg->remoteTransReq == 1)  
		dlc |= MCP_RTR_MASK;  /* if RTR set bit in byte8*/
		
    	mcp2510_setRegister(pInst, mcp_addr+4, dlc );  /* write the RTR and DLC*/
    		
}


STATUS mcp2510_modifyRegister(VXB_DEVICE_ID pInst, const UINT8 address, 
	const UINT8 mask, const UINT8 data)
{
	UINT8 cmd[4];
  	SPI_TRANSFER spi_params;	
	cmd[0] = MCP_BITMOD;
	cmd[1] = address;
	cmd[2] = mask;
	cmd[3] = data;
   	spi_params.rxBuf = NULL;
   	spi_params.txBuf = cmd;
   	spi_params.rxLen = 0;
   	spi_params.txLen = 4;
   	spi_params.usDelay = MCP2510_SPI_DELAY_US;
	
   	if (vxbSpiTransfer (pInst, &spi_params) != OK)
   	{
      		VXBSPIMCP2510_DBG_MSG(VXBMCP2510_DBG_ERR,"MCP2510: Writing register %#x failed.", address,2,3,4,5,6);
      		return ERROR;
   	}
	#if 0
     else
	{
		VXBSPIMCP2510_DBG_MSG(VXBMCP2510_DBG_INFO,"MCP2510: Writing register %#x ok.", address,2,3,4,5,6);
	}
	 #endif 
	 
   	return OK;
		

}


LOCAL void mcp2510_start_transmit(VXB_DEVICE_ID pInst, const UINT8 buffer_sidh_addr)
{
	
	mcp2510_modifyRegister(pInst, buffer_sidh_addr , MCP_TXB_TXREQ_M, MCP_TXB_TXREQ_M );
}





STATUS vxbSpiMCP2510_TxMsg_1
(
      VXB_DEVICE_ID pInst,
      CANMSG* msg
)

{
	UINT8 res, txbuf_n = 0;
	UINT8 count = 0;

	if (msg->canLength<0||msg->canLength>8)				
		return (ERROR);
	
	do 	
	{
		res = mcp2510_getNextFreeTXBuf(pInst, &txbuf_n); 
		count++;
		
	} while ((res == MCP_ALLTXBUSY )&&(count < 3));


	if (res == MCP_ALLTXBUSY ) 
	{
		/*logMsg("can no tx buffer\n",0,0,0,0,0,0);*/
		return ERROR;
	}
	
	mcp2510_write_canMsg(pInst, txbuf_n, msg);
	
	mcp2510_start_transmit(pInst, txbuf_n - 1 );/* TXBnCTRL_addr = TXBnSIDH_addr - 1*/
	return OK;
		
#if 0

   UINT8 buffer[MCP2510_CMD_LOADTXB_LEN];

   // now all input parameters are ignored
   // check length

   buffer[0] = MCP2510_CMD_LOADTXB;    /* now we use only first buffer */
   buffer[1] = 0x00;    /* standard identifier - high byte */
   buffer[2] = 0x60;    /* standard identifier - low byte, EXIDE bit cleared */
   buffer[3] = 0x00;    /* extended identifier - high byte */
   buffer[4] = 0x00;    /* extended identifier - low byte */
   buffer[5] = length;

   memcpy(&buffer[6], data, length);

   //  encapsulate following in some function
   SPI_TRANSFER spi_params;


      spi_params.rxBuf = NULL;
      spi_params.txBuf = buffer;
      spi_params.rxLen = 0;
      spi_params.txLen = 6 + length;
      spi_params.usDelay = MCP2510_SPI_DELAY_US;

      if (vxbSpiTransfer (pInst, &spi_params) != OK)
      {
         printf ("MCP2510: Writing TX setup sequence failed.\n");
         return ERROR;
      }
   /* Previous operations only put data into buffers. To request frame transmision, the RTS command has to be sent. */
   vxbSpiMCP2510_RequestToSend(pInst, channel);
   #endif
}

LOCAL void mcp2510_ISR_t0(MCP2510_DEV *pDev)
{
	int i = 0;
	FOREVER
	{
		if(semTake(pDev->sem_isr,WAIT_FOREVER) == ERROR);
		{
			i++;
			if(i > 2000)
			{
				/*logMsg("wll0\n",0,0,0,0,0,0);*/
				i = 0;
			}
		}
		mcp2510_ISR(pDev);
	}
}

LOCAL UINT8 mcp2510ReadRxID(VXB_DEVICE_ID pInst, UINT8 addr, UINT8 *ext, long *canid, UINT8 *rtr)
{
	UINT8 buf[6];
	*ext = 0;
	*canid = 0;
	*rtr = 0;
	

	vxbSpiMCP2510_ReadNRegisters(pInst,addr,buf,5);

	
	*canid = (buf[0]<<3) + (buf[1]>>5);
	
	
	if ((buf[1]&MCP_TXB_EXIDE_M) == MCP_TXB_EXIDE_M )
	{
		*canid = (*canid<<2) + (buf[1] & 0x03);
		*canid = (*canid<<16) + (buf[2]<<8) + buf[3];
		*ext = 1;
		
	}
	if(*ext == 1)
	{
	/**rtr = ((buf[4]&0x40) == 0x40)||((buf[1]&0x10) == 0x10);*/
		*rtr = ((buf[4]&0x40) == 0x40);
	}
	else
		*rtr =((buf[1]&0x10) == 0x10); 
	
	return (buf[4]&0x0f);/*�������ݳ���*/
	
}

LOCAL void mcp2510ReadRxBuf(VXB_DEVICE_ID pInst, UINT8 addr, UINT8 *rbuf)
{
	UINT8 buf[9];

	vxbSpiMCP2510_ReadNRegisters(pInst,addr,buf,8);	
	/*for(i=0; i<8; i++)
		{
			*(rbuf + i) = buf[i+1];
		}*/
	memcpy(rbuf,buf,8);
}


LOCAL void mcp2510ReadRx(VXB_DEVICE_ID pInst, UINT8 ridcmd,UINT8 rbufcmd, CANMSG *msg)
{
	msg->canLength = mcp2510ReadRxID( pInst, ridcmd, &msg->frameFormat, &msg->canID, &msg->remoteTransReq);
	mcp2510ReadRxBuf( pInst, rbufcmd, msg->cbuf);
}


LOCAL void mcp2510_ISR(MCP2510_DEV *pDev)
{     
	UINT8 intval,eflag;
   	 CANMSG 			canMsg1;
 	CANMSG 			*canMsg;
	MSG_Q_ID 		msgQId;
	SEM_ID			semTX;

	VXB_DEVICE_ID   pInst;
	int i=0,j=0;

	/*int key;*/
	/*key = intLock();*/
	/*vxbIntDisable(pInst, 0, mcp2510_ISR, pDev);*/
	pInst=pDev->pInst;

	
	canMsg = &canMsg1;
	
	msgQId = pDev->can_msgQId;
	semTX = pDev->can_semTx;	
		
	vxbSpiMCP2510_ReadRegister(pInst, MCP_CANINTF,&intval);

	
	
	/*logMsg("error in  can bus  int intval = %x\n",intval,0,0,0,0,0);*/
	/*while((intval&0x07) !=0)*/
	while(intval != 0)
	{
	/*	logMsg("can bus  int intval = %x\n",intval,0,0,0,0,0);*/

		if(intval&MCP_RX0IF)
		{
			mcp2510ReadRx( pInst, CMD_RXB0SIDH,CMD_RXB0D0,canMsg);
			/*msgQSend(msgQId,(char*)canMsg,sizeof(CANMSG),NO_WAIT,MSG_PRI_NORMAL);*/
				
			if((msgQSend(msgQId,(char*)canMsg,sizeof(CANMSG),NO_WAIT,MSG_PRI_NORMAL))==ERROR)
			{
				i++;
				if(i>300)
				{
					logMsg("error in  can msgQsend\n",0,0,0,0,0,0);	
					i=0;
				}
			}
			canIsrCountRcv0++;
		}
		
		if(intval&MCP_RX1IF)
		{
			mcp2510ReadRx( pInst, CMD_RXB1SIDH,CMD_RXB1D0,canMsg);
			/*msgQSend(msgQId,(char*)canMsg,sizeof(CANMSG),NO_WAIT,MSG_PRI_NORMAL);*/
				
			if((msgQSend(msgQId,(char*)canMsg,sizeof(CANMSG),NO_WAIT,MSG_PRI_NORMAL))==ERROR)
			{ 
				j++;
				if(j>300)
				{
					logMsg("error in  can msgQsend\n",0,0,0,0,0,0);
					j=0;
				}
			}
			canIsrCountRcv1++;			  
		}

		#if 1
		if (intval&0x1c)
		{
			semGive(semTX);
			canIsrCountSend++;
		}
		#else
		/* only use sendbuffer0  */
		if (intval&MCP_TX0IF)
			semGive(semTX);
		/*semGive(pDev->sem_isr);*/
		#endif
			
		if(intval&0xa0)
		{

			vxbSpiMCP2510_ReadRegister(pInst, MCP_EFLG,&eflag);
			/*logMsg("error in  can bus  int intval = %x eflag = %x\n",intval,eflag,0,0,0,0);*/

			/*taskDelete(taskISR);*/
			/*UCHAR eflag = mcp2510_readRegister(canNum, MCP_EFLG);*/
			
		}	
						
		mcp2510_modifyRegister(pInst, MCP_CANINTF,intval,0x00);
		vxbSpiMCP2510_ReadRegister(pInst, MCP_CANINTF,&intval);
		/*logMsg("can bus  int intval = %x\n",intval,0,0,0,0,0);*/	
	#if 0
		if(intval&MCP_TX0IF)
			{
				mcp2510_modifyRegister(canNum, MCP_CANINTF,MCP_TX0IF,0);
				semGive(semTX);
			}
		if(intval&MCP_TX1IF)
			{
				mcp2510_modifyRegister(canNum, MCP_CANINTF,MCP_TX1IF,0);
				semGive(semTX);
			}
		if(intval&MCP_TX2IF)
			{
				mcp2510_modifyRegister(canNum, MCP_CANINTF,MCP_TX2IF,0);
				semGive(semTX);
			}
		if(intval&MCP_ERRIF)
			{
				
				mcp2510_modifyRegister(canNum, MCP_CANINTF,MCP_ERRIF,0);
				error = mcp2510_readRegister(canNum, MCP_EFLG);
				logMsg("ERRIF error = %x!\n",error,0,0,0,0,0);
				semGive(semTX);
			}

		if(intval&MCP_MERRF)
			{
				logMsg("MEERF error!\n",0,0,0,0,0,0);
			}
	#endif
  }

	vxbIntEnable (pInst, 0, (VOIDFUNCPTR)mcp2510_ISR_IRQx, pDev);
/*intUnlock(key);*/
}


LOCAL void mcp2510_ISR_IRQx(MCP2510_DEV *pDev)
{
	VXB_DEVICE_ID pInst;
	pInst=pDev->pInst;
    vxbIntDisable(pInst, 0, mcp2510_ISR_IRQx, pDev);
	
	semGive(pDev->sem_isr);
}

LOCAL void sys_mcp2510Int_config(MCP2510_DEV *pDev)  /*connect  interrupt*/
{
       int key;
	VXB_DEVICE_ID pDevGpio;
	FT_GPIO_DRVCTRL * pCtrl;

	/* Connect  interrupts */
	key = intCpuLock();

#if 0
	pDevGpio = vxbInstByNameFind("ftGpio", 1);
	pCtrl = pDevGpio->pDrvCtrl;
	gpioModeSetTest (1, 12, GPIO_MODE_INT);
	pCtrl->gpioISRSet(pDevGpio, 12, mcp2510_ISR_IRQx,pDev); 
	pCtrl->gpioIntEnable(pDevGpio,12); 
#endif
	vxbIntConnect (pDev->pInst, 0, (VOIDFUNCPTR)mcp2510_ISR_IRQx, pDev);
	vxbIntEnable (pDev->pInst, 0, (VOIDFUNCPTR)mcp2510_ISR_IRQx, pDev);

	intCpuUnlock(key);
}

 STATUS mcp2510_setCANCTRL_Mode(VXB_DEVICE_ID pInst, const UINT8 newmode)
{
	UINT8 i;
	int j;
	
	mcp2510_modifyRegister(pInst, MCP_CANCTRL, MODE_MASK, newmode);
	
	for(j=0;j<500;j++)
	{
		/* verify as advised in datasheet*/
		vxbSpiMCP2510_ReadRegister(pInst, MCP_CANSTAT,&i);
		i &= MODE_MASK;

		if ( i == newmode ) 
		{
		 	return OK; 
		}
		taskDelay(0);
	}
	
	return ERROR;
	
}


LOCAL STATUS mcp2510_configRate(VXB_DEVICE_ID pInst, unsigned int canbaud)
{
	UINT8 set, cfg1, cfg2, cfg3;
	
	set = 0;
	
	switch (canbaud) 
	{     
              case (1000):
	              cfg1 = MCP_16MHZ_1MBPS_CFG1;
	              cfg2 = MCP_16MHZ_1MBPS_CFG2;
	              cfg3 = MCP_16MHZ_1MBPS_CFG3;
                  	set = 1;
		       break;

		case (800):
	              cfg1 = MCP_16MHZ_800KBPS_CFG1;
	              cfg2 = MCP_16MHZ_800KBPS_CFG2;
	              cfg3 = MCP_16MHZ_800KBPS_CFG3;
                  	set = 1;
		       break;

	       case (500):
	              cfg1 = MCP_16MHZ_500KBPS_CFG1;
	              cfg2 = MCP_16MHZ_500KBPS_CFG2;
	              cfg3 = MCP_16MHZ_500KBPS_CFG3;
                  	set = 1;
		       break;
              case (250):
	              cfg1 = MCP_16MHZ_250KBPS_CFG1;
	              cfg2 = MCP_16MHZ_250KBPS_CFG2;
	              cfg3 = MCP_16MHZ_250KBPS_CFG3;
                  	set = 1;
		       break;
	      case (125):
	              cfg1 = MCP_16MHZ_125KBPS_CFG1;
	              cfg2 = MCP_16MHZ_125KBPS_CFG2;
	              cfg3 = MCP_16MHZ_125KBPS_CFG3;
                  	set = 1;
		       break;
				
		default:
			set = 0;
			break;
	}
	
	if (set) 
	{
		mcp2510_setRegister(pInst, MCP_CNF1, cfg1);
		mcp2510_setRegister(pInst, MCP_CNF2, cfg2);
		mcp2510_setRegister(pInst, MCP_CNF3, cfg3);
		return OK;
	}
	else
	{
		return  ERROR;
	}
} 



LOCAL void mcp2510_initCANBuffers(VXB_DEVICE_ID pInst)
{
	UINT8 i, a1, a2, a3;
	
	
	 	 /*  and standard frames
	 	 Mark all filter bits as don't care:*/
    	mcp2510_write_can_id( pInst, MCP_RXM0SIDH, 0, 0);
    	mcp2510_write_can_id( pInst, MCP_RXM1SIDH, 0, 0);
    	/*Anyway, set all filters to 0:*/
    	mcp2510_write_can_id( pInst, MCP_RXF0SIDH, 1, 0); /*RXB0: extended */
    	mcp2510_write_can_id( pInst, MCP_RXF1SIDH, 0, 0); /* AND standard*/
    	mcp2510_write_can_id( pInst, MCP_RXF2SIDH, 1, 0); /* RXB1: extended */
    	mcp2510_write_can_id( pInst, MCP_RXF3SIDH, 0, 0); /*AND standard*/
    	mcp2510_write_can_id( pInst, MCP_RXF4SIDH, 0, 0);
    	mcp2510_write_can_id( pInst, MCP_RXF5SIDH, 0, 0);
	
    	/* Clear, deactivate the three transmit buffers*/
    	/* TXBnCTRL -> TXBnD7*/
    	a1 = MCP_TXB0SIDH;
    	a2 = MCP_TXB1SIDH;
    	a3 = MCP_TXB2SIDH;
    	for (i = 0; i < 13; i++) 
    	{ /*in-buffer loop*/
			mcp2510_setRegister(pInst,a1, 0);
			mcp2510_setRegister(pInst,a2, 0);
			mcp2510_setRegister(pInst,a3, 0);
			a1++;
			a2++;
			a3++;
    	}
	
    	/*and clear, deactivate the two receive buffers.*/
    	mcp2510_setRegister(pInst, MCP_RXB0CTRL, 0);
    	mcp2510_setRegister(pInst, MCP_RXB1CTRL, 0);
    	mcp2510_setRegister(pInst, MCP_TXB0CTRL, 0x03);
    	mcp2510_setRegister(pInst, MCP_TXB1CTRL, 0x02);
    	mcp2510_setRegister(pInst, MCP_TXB2CTRL, 0x00);
}

LOCAL void mcp2510SetRxMaskFilter(VXB_DEVICE_ID pInst, const UINT8 addr, long mask, UINT8 ext)
{
	mcp2510_write_can_id(pInst, addr, ext, mask);
}

LOCAL void mcp2510_initINT(VXB_DEVICE_ID pInst)    /*set INT register*/
{
      mcp2510_setRegister(pInst,MCP_CANINTE,0x1F);			/*2020-01-15   �������߳�����ӡ��wll0��*/
      /*mcp2510_setRegister(pInst,MCP_CANINTE,0xA7);*/      /*only use txb buffer 0 */
	  /*mcp2510_setRegister(pInst,MCP_CANINTE,0xBF);*/				  
	  mcp2510_setRegister(pInst,MCP_CANINTF,MCP_NO_INT);
}

LOCAL STATUS  mcp2510_init (VXB_DEVICE_ID pInst,long canmask,long canid,unsigned int canbaud,UINT8 ext)
{
	
	vxbSpiMCP2510_Reset(pInst);
	

	if (ERROR == mcp2510_setCANCTRL_Mode(pInst, MODE_CONFIG))	
	{
		printf("\nset mcp2510 config mode is wrong!\n");
		return ERROR;
	}
	
	if (ERROR == mcp2510_configRate(pInst,canbaud))
	{
		printf("\nThe canbaud is wrong!\n");
		return ERROR;
	}
	
	mcp2510_initCANBuffers(pInst);
	
	#if (DEBUG_RXANY==1)
	{
				 	/*enable both receive-buffers to receive any message, and enable rollover*/
		mcp2510_modifyRegister(pInst, MCP_RXB0CTRL, 
						MCP_RXB_RX_MASK | MCP_RXB_BUKT_MASK, 
						MCP_RXB_RX_ANY | MCP_RXB_BUKT_MASK);
		mcp2510_modifyRegister(pInst, MCP_RXB1CTRL, MCP_RXB_RX_MASK, 
						MCP_RXB_RX_ANY);
	}
	#else 
	{	
				/*enable both receive-buffers to receive messages
				with std. and ext. identifiers
				and enable rollover*/
				
		mcp2510_modifyRegister(pInst, MCP_RXB0CTRL, 
						MCP_RXB_RX_MASK | MCP_RXB_BUKT_MASK, 
						MCP_RXB_RX_STDEXT | MCP_RXB_BUKT_MASK );
		mcp2510_modifyRegister(pInst, MCP_RXB1CTRL, MCP_RXB_RX_MASK, 
						MCP_RXB_RX_STDEXT);
				
				/*set mask register 0x1fffffff*/
		mcp2510SetRxMaskFilter(pInst, MCP_RXM0SIDH, canmask,ext);
		mcp2510SetRxMaskFilter(pInst, MCP_RXM1SIDH, canmask,ext);

		mcp2510SetRxMaskFilter(pInst, MCP_RXF0SIDH, canid, ext);
		mcp2510SetRxMaskFilter(pInst, MCP_RXF1SIDH, canid, ext);
				
	}
	#endif
	

	mcp2510_initINT(pInst);
	
	/*mcp2510_modifyRegister(pInst, MCP_CANCTRL, 0x08, 0x08);��ʼ�����η���*/
	#if 1
		if (ERROR == mcp2510_setCANCTRL_Mode(pInst, MODE_NORMAL))	
		{
			printf("\nset mcp2510 normal work mode is wrong\n");
			return ERROR;
		}
	#else

		if (ERROR == mcp2510_setCANCTRL_Mode(pInst, MODE_LOOPBACK))	
		{
			printf("\nset mcp2510 loopback work mode is wrong\n");
			return ERROR;
		}

	#endif

	return OK;	
}




STATUS  canConfig(MCP2510_DEV *pDev,long canmask,long canid,unsigned int canbaud, UCHAR ext)
{
	/*printf("canConfig\n");*/
	
	/*set GPIO1_12 direction to input*/
	writel((readl(0x28035004) & 0xEFFF),0x28035004);  
	/*Set GPIO1_12 interrupt bits no mask*/
	writel((readl(0x2803501C) & 0xEFFF),0x2803501C);
	/*Set GPIO1_12 interrupt to level sensitive*/
	writel((readl(0x28035020) & 0xEFFF),0x28035020);
	/*Set GPIO1_12 interrupt to low active*/
	writel((readl(0x28035024) & 0xEFFF),0x28035024);
	/*Set GPIO1_12 port bit to interrupt*/
	writel((readl(0x28035018) | 0x1000),0x28035018);

	if(( pDev->can_msgQId=msgQCreate(NUM_MSGS,sizeof(CANMSG),MSG_Q_FIFO))==NULL)	
	{
  		perror("cannot creat msgQ");
  		return ERROR;
  	}

	pDev->can_semTx = semBCreate (SEM_Q_PRIORITY ,SEM_EMPTY);
	if(pDev->can_semTx == ERROR)
	{
		perror("pDev->can_semTx Create is error .\n");
		return ERROR;
	}
	pDev->sem_isr = semBCreate (SEM_Q_PRIORITY ,SEM_EMPTY);
	
	if(pDev->sem_isr == ERROR)
	{
	    perror("pDev->sem_isr Create is error .\n");
		return ERROR;
	}
	else
	{
		/*printf("pDev->sem_isr=%#x \n",pDev->sem_isr);*/
	}
	pDev->taskID=taskSpawn("tcan0", 80, 0, 0x40000,(FUNCPTR)mcp2510_ISR_t0,pDev,0,0,0,0,0,0,0,0,0);
	if(pDev->taskID == ERROR)
	{
		perror("tcan0 task Create is error .\n");
		return ERROR;
	}

	if (ERROR == mcp2510_init(pDev->pInst ,canmask,canid,canbaud,ext))
	{
	    perror("[mcp2510_init] is error .\n");
		return ERROR;
	}
	sys_mcp2510Int_config(pDev);

	return OK;	
	
}

LOCAL STATUS canSend(MCP2510_DEV *pDev,CANMSG msg)
{
#if 0	
	CANMSG msg={0};
	/*delete by lrl
	int i;
	can_initMessageStruct(&msg);	
	*/
	msg.canID = canid;
	msg.canlength = length;
	msg.frameFormat =1;
	msg.remoteTransReq = 1;
#endif	
	/* delete by lrl
	for (i=0; i<length; i++)
	{
		msg.cbuf[i] = *(buff+i);
	}
	*/
	
	/*memcpy(msg.cbuf,buff,length);*/
	if (ERROR == vxbSpiMCP2510_TxMsg_1(pDev->pInst, &msg))
	{
		logMsg("\nvxbSpiMCP2510_TxMsg_1 !\n",0,0,0,0,0,0);
		return ERROR;
	}
	if (ERROR == semTake(pDev->can_semTx,sysClkRateGet()/50))  /*time out by lrl 5->10  2021-08-13 timeout from 500 to 0*/
	{
		logMsg("\ncan TX0 time out!\n",0,0,0,0,0,0);
		return ERROR;
	}
	else 
		
		return OK;	
}


LOCAL STATUS canRcv(MCP2510_DEV *pDev, CANMSG *RcvMsg)
{
	

	if((msgQReceive(pDev->can_msgQId,(char*)RcvMsg,sizeof(CANMSG),WAIT_FOREVER))==ERROR)
	{  
		/*perror("error in canRcv ,can0!");	*/
		return ERROR;  	    
	}   
	else 
		return OK;	
	

}

/*---------------------testCan-------------------------*/
#if 1

void countIsr()
{
	printf(" rcv ISR : %d \n\n",(canIsrCountRcv0+canIsrCountRcv1));
	printf("send ISR : %d \n\n",canIsrCountSend);
	printf("ISR : %d   \n\n",countCan);
}

void canTest(int channel)
{
	
	VXB_DEVICE_ID pInst;
	MCP2510_DEV *pDev=NULL;
	pDev=&MCP2510Device[channel];	
	
	
	if(channel>=0 && channel<4)
	{
		
		pDev->pInst = vxbInstByNameFind (DRIVER_NAME_MCP2510, channel);
	}
	else
	{
		
		printf("channel is out of range!\n");
		return;
	}

	/*canConfig(pDev,0,0xff,800,0);*/
	canConfig(pDev,0,0xff,800,0);
	
	taskSpawn("send", 105, 0, 0x40000,(FUNCPTR)can_testTransmit,pDev,0,0,0,0,0,0,0,0,0);

	taskSpawn("receive", 105, 0, 0x40000,(FUNCPTR)can_testReceive,pDev,0,0,0,0,0,0,0,0,0);


}




void can_testTransmit(MCP2510_DEV *pDev)
{
	int i,c;
	ULONG j=1;
	long canid = 0x10;
	UINT8 buf[20];
	
	CANMSG msg={0};	

	
	/*while(1)*/
	for(c=0;c<20000;c++)
	{
		for (i=0; i<8; i++)
		{
			*(buf + i) = j + i;
		}

		msg.canID = canid;
		msg.canLength = 8;
		msg.frameFormat =0;
		msg.remoteTransReq = 0;
		memcpy(msg.cbuf,buf,msg.canLength);

		canSend(pDev, msg);
		
		/*taskDelay(1);*/
#if 0
		canRcv(pInst,&msg);
		if (msg.remoteTransReq == 1)
			printf("\nReceive Remote Message!\n");
		else 
		{

			printf("\n canid is %x\n\n can length is %x\n",msg.canID,msg.canlength);
			printf("\n can data is \n");
			for (i=0; i<msg.canlength; i++)
				{
					printf("  %d \n ",msg.cbuf[i]);
				} 
			printf("\n<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n");
			printf("\n>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\n");
			
		}
		taskDelay(30);
										
#endif
		
		
		j=j+8;
	}

}



void can_testReceive(MCP2510_DEV *pDev)
{
	CANMSG msg;

	int i = 0;
    
	UINT32 c,k;
	k=0;
	c=0;
	/*while(1)*/
	for(c=0;c<20050;c++)
	{
		

		canRcv(pDev,&msg);
		if (msg.remoteTransReq == 1)
			printf("\nReceive Remote Message!\n");
		else 
		{
			k++;
#if 0
			printf("\n canid is %x\n\n can length is %x\n",msg.canID,msg.canLength);
			printf("\n can data is \n");
			for (i=0; i<msg.canLength; i++)
			{
				printf("  %d \n ",msg.cbuf[i]);
			} 
			printf("\n------------------\n");
#endif			
		}
		taskDelay(0);

								
	}
	printf("k=%x",k);

	
}
#endif
/*---------------------END----------------------------*/


/*-------------------customer demand-------------------*/

#if 0
/******************************************************/
/*
 CANOpen
 Description: open can
 Parameter:
 	 int channel:0-1
 Return:
 	 STATUS: 0  OK
 	        -1  ERROR
 */
/*******************************************************/
STATUS CANOpen(int channel)
{
	VXB_DEVICE_ID pInst;
	if(channel>=0 && channel<2)
	{
		pInst=vxbInstByNameFind (DRIVER_NAME_MCP2510, channel);
	
		return OK;
	}
	else
	{
		logMsg("error can channel is %d!\n",channel,0,0,0,0,0);
		return ERROR;
	}		
}
#endif
/***************************************************/
/*
 CANOpen
 Description: close can
Parameter:
 	 int channel:0-1
 Return:
 	 STATUS: 0  OK
 	        -1  ERROR
 */
/****************************************************/
STATUS CANClose(int channel)
{
	MCP2510_DEV *pDev = NULL;
	pDev=&MCP2510Device[channel];
	
	if(pDev->taskID>0)
	{
		taskDelete(pDev->taskID);
		pDev->taskID=-1;
	}
	vxbIntDisable(pDev->pInst, 0, mcp2510_ISR_IRQx, pDev);
	vxbIntDisconnect(pDev->pInst, 0, (VOIDFUNCPTR)mcp2510_ISR_IRQx, pDev);
    return OK;
}


/******************************************************/
/* CANInit
 *Description: intitialize the CAN controller
 *Import Parameter:
 *	 int channel:0-1
 *	 CANInitInBuf canCfg:see struct CANInitInBuf
 *Return Parameter:
 *	 STATUS: 0  OK
 *	        -1  ERROR
 */	        
 /******************************************************/

STATUS CANInit(int channel,CANInitInBuf canCfg)
{
	
	VXB_DEVICE_ID pInst;
	MCP2510_DEV *pDev = NULL;
	pDev=&MCP2510Device[channel];
	
	if(channel>=0 && channel<2)
	{		
		if((pDev->pInst = vxbInstByNameFind (DRIVER_NAME_MCP2510, channel))!=NULL)
		{

			if((canConfig(pDev,canCfg.localIDMask,canCfg.localID,canCfg.baudRate,canCfg.frameFormat))==OK)
			{
				logMsg("CAN%d initialize succeed!\n",channel,0,0,0,0,0);
				return OK;
			}
			else
			{
				logMsg("CAN%d initialize failure!\n",channel,0,0,0,0,0);
				return ERROR;
			}
		}
	}
	else
	{
		logMsg("error can channel is %d!\n",channel,0,0,0,0,0);
		return ERROR;
	}	
	
}

/*******************************************************/
/**
**  FUNCTION:
**	CANSend()
**  DESCRIPTION:
**  	Send a fream message to other Can channel. 
**  PARAMETERS:
**  	@channel		: 0-1.
**	@CanSmsg 	: message struct to send.
**  RETURN:
**	0(OK)		: success
**	-1(ERROR)	: failure
**/
/*****************************************************/

STATUS CANSend(int channel, CANMSG CanSmsg)
{
	VXB_DEVICE_ID pInst;
	MCP2510_DEV *pDev = NULL;
	pDev=&MCP2510Device[channel];
	
	if(channel>=0 && channel<2)
	{		
		pDev->pInst = vxbInstByNameFind (DRIVER_NAME_MCP2510, channel);
		
		if((canSend(pDev, CanSmsg))==OK)
		{
			/*logMsg("CAN%d send succeed!\n",channel,0,0,0,0,0);*/
			return OK;
		}
		else
		{
			/*logMsg("CAN%d send failure!\n",channel,0,0,0,0,0);*/
			return ERROR;
		}
	}
	else
	{
		/*logMsg("error can channel is %d!\n",channel,0,0,0,0,0);*/
		return ERROR;
	}	
}

#if 1
/*******************************************************/
/**
**  FUNCTION:
**	CANReceive()
**  DESCRIPTION:
**  	receive a fream message from other Can channel. 
**  PARAMETERS:
**  	@channel		: 0-1.
**	@CanRmsg 	: message struct to Receive.
**  RETURN:
**	0(OK)		: success
**	-1(ERROR)	: failure
**/
/*****************************************************/

STATUS CANReceive(int channel, CANMSG *CanRmsg)
{
	VXB_DEVICE_ID pInst;
	MCP2510_DEV *pDev = NULL;
	pDev=&MCP2510Device[channel];
	
	if(channel>=0 && channel<2)
	{		
		pDev->pInst = vxbInstByNameFind (DRIVER_NAME_MCP2510, channel);

		if((canRcv(pDev, CanRmsg))==OK)
		{
			/*logMsg("CAN%d send succeed!\n",channel,0,0,0,0,0);*/
			return OK;
		}
		else
		{
			logMsg("CAN%d recv failure!\n",channel,0,0,0,0,0);
			return ERROR;
		}
	}
	else
	{
		logMsg("error can channel is %d!\n",channel,0,0,0,0,0);
		return ERROR;
	}	
}

#endif



/*------------------------end---------------------------*/


STATUS WWCan_ShowRegisters (UINT8 address, UINT8 count)
{
	VXB_DEVICE_ID pInst;
		
	pInst = vxbInstByNameFind (DRIVER_NAME_MCP2510, 0);

  	if (pInst == NULL)
   	{
    	printf ("Can_ShowRegisters: %s instance was not found on VxBus.\n", DRIVER_NAME_MCP2510);
       	return ERROR;
   	}
  	  	
   	return vxbSpiMCP2510_ShowRegisters(pInst, address, count);
}

STATUS WWCan_TxMsg(void)
{
   	VXB_DEVICE_ID pInst;
   	const UINT8 frame_payload[MCP2510_CAN_MAX_DLC] = {1, 2, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88};

   	pInst = vxbInstByNameFind (DRIVER_NAME_MCP2510, 0);
   
   	return vxbSpiMCP2510_TxMsg(pInst, 0, 0, 0, &frame_payload, 2);
}

/*2020-01-18 add can status*/
STATUS WWCan_Status(void)
{
	VXB_DEVICE_ID pInst;
	UINT8 status_val;
	
	pInst = vxbInstByNameFind (DRIVER_NAME_MCP2510, 0);
	return vxbSpiMCP2510_Status(pInst,&status_val);
}

STATUS WWCan_Reset(void)
{
   	VXB_DEVICE_ID pInst;


		#if 0
		*((UINT32*)(0xffe500c0))  =  0x80000000;/*e500,�½����ж�*/
		#endif

   	pInst = vxbInstByNameFind (DRIVER_NAME_MCP2510, 0);
   	return vxbSpiMCP2510_Reset(pInst);
}

void WWCan_initBuffers(void)
{
	VXB_DEVICE_ID pInst;
	
	pInst = vxbInstByNameFind (DRIVER_NAME_MCP2510, 0);
	mcp2510_initCANBuffers(pInst);
}

STATUS WWCan_Init(void)
{
   	VXB_DEVICE_ID pInst;

   	pInst = vxbInstByNameFind (DRIVER_NAME_MCP2510, 0);
   	return vxbSpiMCP2510_Init(pInst);
}


#ifdef VXBMCP2510_DBG_ON

/*******************************************************************************
*
* vxbMCP2510_pDrvCtrlShow - show pDrvCtrl for sample driver
*
* This routine displays a message about pDrvCtrl of the specified instance.
*
* RETURNS: N/A
*
* ERRNO: not set
*/

int vxbMCP2510_pDrvCtrlShow
    (
    VXB_DEVICE_ID pInst
    )
{
	printf ("pDrvCtrl @ %p\n", pInst->pDrvCtrl);

    return (0);
}

#endif /* vxbMCP2510_DBG_ON */


