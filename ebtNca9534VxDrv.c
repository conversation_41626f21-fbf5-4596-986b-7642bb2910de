/*************************************************
  Copyright (C),  Embed-Te<PERSON> ,TianJin
  File name:       ebtNca9534VxDrv.c
  Description:     NCA9534 GPIO Functions  

*************************************************/

#include <vxWorks.h>

/* debug */
#define NCA9534_DBG_ON
#ifdef NCA9534_DBG_ON
#define NCA9534_GPIO_DBG(fmt...)	printf (fmt)
#else
#define NCA9534_GPIO_DBG(fmt...)
#endif	/* NCA9534_DBG_ON */


#define NCA9534_NAME 	"NCA9534"

#define NCA9534_INPUT_REG 		0x00
#define NCA9534_OUTPUT_REG 	0x01
#define NCA9534_CONFIG_REG 	0x03     /*bit0:output mode, bit1:input mode  default:0xFF*/

#define NCA9534_GPIO_PIN_MAX  0x08

#define GPIO_LOW                        0x00
#define GPIO_HIGH                       0x01

static UINT8 nca9534_Data[8]={0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff};
static UINT8 nca9534_Config[8]={0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff};


STATUS NCA9534GpioSet (UINT8 unit,UINT8 lineNo,UINT8 value)
{
	UINT8  gpioData = nca9534_Data[unit];
	UINT8 gpioMode=nca9534_Config[unit];

	if (lineNo > NCA9534_GPIO_PIN_MAX)
		return (ERROR);
		
	if (value == GPIO_HIGH)
	{
	   gpioData |= (0x01 << lineNo);
	 }
	 else if (value == GPIO_LOW)
	 {
		gpioData &= ~(0x01 << lineNo);
	 }

	gpioMode&= ~(0x01 << lineNo);
	 
	NCA9534_GPIO_DBG("unit=%d  gpioData=0x%x gpioMode=0x%x\n",unit,gpioData,gpioMode);
	
	nca9534_Data[unit]=gpioData;
	nca9534_Config[unit]=gpioMode;
	
	vxbI2cByNameWrite(NCA9534_NAME,unit,NCA9534_CONFIG_REG,&gpioMode,1);
	  
	return vxbI2cByNameWrite(NCA9534_NAME,unit,NCA9534_OUTPUT_REG,&gpioData,1);
	
}

STATUS NCA9534GpioGet (UINT8 unit,UINT8 lineNo,UINT8 * gpioData)
{
	UINT8 gpioMode=nca9534_Config[unit];
	
	if(gpioData == NULL)
	{
		return ERROR;
	}
	if (lineNo > NCA9534_GPIO_PIN_MAX)
		return (ERROR);

	gpioMode |= (0x01 << lineNo);
	vxbI2cByNameWrite(NCA9534_NAME,unit,NCA9534_CONFIG_REG,&gpioMode,1);
	nca9534_Config[unit]=gpioMode;
	
	if(vxbI2cByNameRead(NCA9534_NAME,unit,NCA9534_INPUT_REG,gpioData,1)==ERROR)
		return (ERROR);

	*gpioData = (*gpioData >> lineNo) & 0x01;  

	NCA9534_GPIO_DBG("unit=%d  gpioData=0x%x gpioMode=0x%x\n",unit,*gpioData,gpioMode);

	return (OK);	
}

/*----------------test------------------------*/
int testOutGpio(UINT8 val)
{
	UINT8  gpioData;
	UINT8 outputMode=0;
	

	if(val==1)
		gpioData=0;
	else if(val==0)
		gpioData=0xff;

	vxbI2cByNameWrite(NCA9534_NAME,1,NCA9534_CONFIG_REG,&outputMode,1);
	vxbI2cByNameWrite(NCA9534_NAME,1,NCA9534_OUTPUT_REG,&gpioData,1);

	return 0;
}

int testInGpio(void)
{
	UINT8  gpioData = 0;
	UINT8 gpioMode=0xff;
	
	vxbI2cByNameWrite(NCA9534_NAME,1,NCA9534_CONFIG_REG,&gpioMode,1);
	vxbI2cByNameRead(NCA9534_NAME,1,NCA9534_INPUT_REG,&gpioData,1);
	
	printf("data=0x%x\n",gpioData);
	return 0;
}

void testInGpio1(void)
{
	UINT8 i = 0;
	UINT8 data[8]={0};
	
	for (i = 0; i < 8; i++) 
	{
		NCA9534GpioGet(3,i,&data[i]);
	}
}


