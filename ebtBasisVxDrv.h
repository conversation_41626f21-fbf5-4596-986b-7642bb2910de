/*************************************************
  Copyright (C),  Embed-Te<PERSON> ,TianJin
  File name:      ebtBasisVxDrv.h
  Description:     Basis Functions        

    
*************************************************/
#ifndef _BASIS_Drv_H
#define _BASIS_Drv_H

#ifdef __cplusplus
    extern "C" {
#endif /* __cplusplus */


#define LED_ON                        0x00
#define LED_OFF                       0x01


#define BOARD_TYPE_LEN	4
#define BOARD_TYPE_NUM_OFFSET	18
#define BOARD_CRC	0xFFFFFFFFU
#define	BOARD_TYPE_NUM	0xAA55
#define BOARD_TYPE_NUM_CRC_OFFSET	22
      
  
    
#define VERSION_OFFSET 0x7E
#define FPGA_VER_ADDR 0x30
    
    
#define MAX_CPU_NUM 2
#define MAX_LED_NUM 1
#define MAX_GPIO_NUM 1
#define MAX_FS_NUM 1
#define MAX_MOTETSEC_NUM 0
#define MAX_GEI_NUM 2
#define MAX_UART_NUM 1
#define FS_NAME "/mmc0:0"
#define MOTETSEC_NAME "motetsec"
#define GEI_NAME "gem"
#define UART_NAME "/tyC0"
    
typedef struct {
	char name[100];
	UINT8 unit;
}NETDEVICE;
typedef struct {
	UINT8 cpu_num;
	UINT16 led_num;
	UINT16 gpio_num;
	UINT8  fs_num;
	char fs_path[10][100];
	UINT8  netdev_num;
	NETDEVICE netdev[15];
	UINT8  uart_num;
	char uart_name[10][100];
}BOARD_INFO;    

int bsp_slot_get(void);
int bsp_cpu_id_get(void);
UINT32 bsp_hardware_version_get(void);
STATUS output_pw_gate(UINT32 val);
int output_pw_back(void);
int core_voltage_mon(void);
int voltage_5v_mon(void);

#ifdef __cplusplus
    }
#endif /* __cplusplus */

#endif /* _BASIS_Drv_H */

