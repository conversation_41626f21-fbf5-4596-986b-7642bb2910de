/*************************************************
  Copyright (C),  Embed-Te<PERSON> ,TianJin
  File name:      ebtEepromDrv.h
  Author:  nifang     Version:  V1.0      Date: 09Dec2016
  Description:     EEProm Functions  
  
   History:        
  1. Date:
     Author:
     Modification: 
    
*************************************************/
#ifndef _EEprom_Drv_H
#define _EEprom_Drv_H

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */


UINT8 EEpromRead (UINT32 reg);
void EEpromWrite(UINT32 reg, UINT8 val);


#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* _EEprom_Drv_H */

