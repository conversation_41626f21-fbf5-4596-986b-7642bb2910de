/* vxbSpiMCP2510.h - MCP25XX SPI CAN driver header file */ 

/*
 * Copyright (C) 2007-2008, 2010 Wind River Systems, Inc.
 *
 * The right to copy, distribute, modify, or otherwise make use
 * of this software may be licensed only pursuant to the terms
 * of an applicable Wind River license agreement.
 */



#ifndef __INCvxbSpiMCP2510h
#define __INCvxbSpiMCP2510h

/* includes */

#include <vxWorks.h>
#include <vxBusLib.h>
#include <hwif/vxbus/vxBus.h>

/* defines */

typedef struct 		/* MCP2510_DEV */
{
	VXB_DEVICE_ID pInst; 	
	MSG_Q_ID	can_msgQId;
	SEM_ID 	can_semTx;
	SEM_ID 	sem_isr;
	int taskID;
}MCP2510_DEV;


/* typedefs */
typedef struct  
{
      UINT8 brp;       /* baud rate prescaler */
      UINT8 sjw;       /* syncro jump width */
      UINT8 prop_seg;  /* number of time quanta for propagation segment */
      UINT8 tseg1;     /* number of time quanta for segment 1 */
      UINT8 tseg2;     /* number of time quanta for segment 2 */
      BOOL sample3times; /* sample 3 times per bit (if TRUE), otherwise - sample only once */
      
}
vxbSpiMCP2510_CanTimingType;

IMPORT void vxbSpiMCP2510_Register (void);

IMPORT STATUS EthSw_ShowRegisters (UINT8 address, UINT8 count);
IMPORT STATUS EthSw_WriteRegister (UINT8 address, UINT8 value);

METHOD_DECL(WWTemperatureFuncGet);


int vxbMCP2510_pDrvCtrlShow
    (
    VXB_DEVICE_ID pInst
    );


/*=================wll change for mcp2510==========================*/

/*=================add register definition============================*/







typedef struct 
{
	UINT8 	 frameFormat; 		/* 0 = Std or 1 = Ext */
	long 	 canID;       		/* the destination node ID */
 	UINT32   canLength;	  		/* data length code (0 - 8) */
 	UCHAR 	 cbuf[8];     		/* the data to trans/rec */
 	UINT8	 remoteTransReq;   	/* remote transmission request */
}CANMSG;


typedef  struct 
{
	UINT8 frameFormat; /* 0 = Std or 1 = Ext */
	long localID;     /* local CAN node id */
	long localIDMask; /* local CAN node id mask,when localIDMask is 0,canBus can receive any ID can_data */
	unsigned long baudRate;    /* band rate, i.e.125=125Kbit/s 250=250Kbit/s 500=500Kbit/s 800=800Kbit/s 1000=1Mbit/s */
}CANInitInBuf;




#define N_MCP2510_CHANNELS 1




/*------------------ Begin mt----------------*/
#define MCP_SIDH        0
#define MCP_SIDL        1
#define MCP_EID8        2
#define MCP_EID0        3

#define MCP_TXB_EXIDE_M     0x08    /* In TXBnSIDL */
#define MCP_DLC_MASK        0x0F    /* 4 LSBits */
#define MCP_RTR_MASK        0x40    /* (1<<6) Bit 6 */

#define MCP_RXB_RX_ANY      0x60
#define MCP_RXB_RX_EXT      0x40
#define MCP_RXB_RX_STD      0x20
#define MCP_RXB_RX_STDEXT   0x00
#define MCP_RXB_RX_MASK     0x60
#define MCP_RXB_BUKT_MASK   (1<<2)

/*
** Bits in the TXBnCTRL registers.
*/
#define MCP_TXB_TXBUFE_M    0x80
#define MCP_TXB_ABTF_M      0x40
#define MCP_TXB_MLOA_M      0x20
#define MCP_TXB_TXERR_M     0x10
#define MCP_TXB_TXREQ_M     0x08
#define MCP_TXB_TXIE_M      0x04
#define MCP_TXB_TXP10_M     0x03

#define MCP_TXB_RTR_M       0x40   /* In TXBnDLC*/
#define MCP_RXB_IDE_M       0x08    /*In RXBnSIDL*/
#define MCP_RXB_RTR_M       0x40    /*In RXBnDLC*/

#define MCP_STAT_RXIF_MASK   (0x03)
#define MCP_STAT_RX0IF (1<<0)
#define MCP_STAT_RX1IF (1<<1)

#define MCP_EFLG_RX1OVR (1<<7)
#define MCP_EFLG_RX0OVR (1<<6)
#define MCP_EFLG_TXBO   (1<<5)
#define MCP_EFLG_TXEP   (1<<4)
#define MCP_EFLG_RXEP   (1<<3)
#define MCP_EFLG_TXWAR  (1<<2)
#define MCP_EFLG_RXWAR  (1<<1)
#define MCP_EFLG_EWARN  (1<<0)
#define MCP_EFLG_ERRORMASK  (0xF8) /* 5 MS-Bits */

/*----------------End mt----------------*/



/*-------- Define MCP2510 register addresses----------*/

#define MCP_RXF0SIDH	0x00
#define MCP_RXF0SIDL	0x01
#define MCP_RXF0EID8	0x02
#define MCP_RXF0EID0	0x03
#define MCP_RXF1SIDH	0x04
#define MCP_RXF1SIDL	0x05
#define MCP_RXF1EID8	0x06
#define MCP_RXF1EID0	0x07
#define MCP_RXF2SIDH	0x08
#define MCP_RXF2SIDL	0x09
#define MCP_RXF2EID8	0x0A
#define MCP_RXF2EID0	0x0B
#define MCP_CANSTAT		0x0E
#define MCP_CANCTRL		0x0F
#define MCP_RXF3SIDH	0x10
#define MCP_RXF3SIDL	0x11
#define MCP_RXF3EID8	0x12
#define MCP_RXF3EID0	0x13
#define MCP_RXF4SIDH	0x14
#define MCP_RXF4SIDL	0x15
#define MCP_RXF4EID8	0x16
#define MCP_RXF4EID0	0x17
#define MCP_RXF5SIDH	0x18
#define MCP_RXF5SIDL	0x19
#define MCP_RXF5EID8	0x1A
#define MCP_RXF5EID0	0x1B
#define MCP_TEC			0x1C
#define MCP_REC			0x1D
#define MCP_RXM0SIDH	0x20
#define MCP_RXM0SIDL	0x21
#define MCP_RXM0EID8	0x22
#define MCP_RXM0EID0	0x23
#define MCP_RXM1SIDH	0x24
#define MCP_RXM1SIDL	0x25
#define MCP_RXM1EID8	0x26
#define MCP_RXM1EID0	0x27
#define MCP_CNF3			0x28
#define MCP_CNF2			0x29
#define MCP_CNF1			0x2A
#define MCP_CANINTE		0x2B
#define MCP_CANINTF		0x2C
#define MCP_EFLG			0x2D
#define MCP_TXB0CTRL	0x30
#define MCP_TXB1CTRL	0x40
#define MCP_TXB2CTRL	0x50
#define MCP_RXB0CTRL	0x60
#define MCP_RXB0SIDH	0x61
#define MCP_RXB1CTRL	0x70
#define MCP_RXB1SIDH	0x71
#define MCP_TXB0SIDH	0x31
#define MCP_TXB1SIDH	0x41
#define MCP_TXB2SIDH	0x51
#define MCP_TXB0DLC		0x35
#define MCP_TXB1DLC		0x45
#define MCP_TXB2DLC		0x55

#define MCP_RXBUF_0 (MCP_RXB0SIDH)
#define MCP_RXBUF_1 (MCP_RXB1SIDH)

#define MCP_TXBUF_0 (MCP_TXB0SIDH)
#define MCP_TXBUF_1 (MCP_TXB1SIDH)
#define MCP_TXBUF_2 (MCP_TXB2SIDH)


#define MCP_TX_INT		0x1C		/* Enable all transmit interrupts*/
#define MCP_TX01_INT	0x0C		/* Enable TXB0 and TXB1 interrupts*/
#define MCP_RX_INT		0x03		/* Enable receive interrupts*/
#define MCP_NO_INT		0x00		/*Disable all interrupts*/

#define MCP_TX01_MASK	0x14
#define MCP_TX_MASK		0x54

/* Define SPI Instruction Set*/

#define MCP_WRITE		0x02

#define MCP_READ		0x03

#define MCP_BITMOD		0x05

#define MCP_LOAD_TX0	0x40
#define MCP_LOAD_TX1	0x42
#define MCP_LOAD_TX2	0x44

#define MCP_RTS_TX0		0x81
#define MCP_RTS_TX1		0x82
#define MCP_RTS_TX2		0x84
#define MCP_RTS_ALL		0x87

#define MCP_READ_RX0	0x90
#define MCP_READ_RX1	0x94

#define MCP_READ_STATUS	0xA0

#define MCP_RX_STATUS	0xB0

#define MCP_RESET		0xC0


/*CANCTRL Register Values*/

#define MODE_NORMAL     		0x00
#define MODE_SLEEP      		0x20
#define MODE_LOOPBACK   		0x40
#define MODE_LISTENONLY 	0x60
#define MODE_CONFIG     		0x80
#define MODE_POWERUP		0xE0
#define MODE_MASK			0xE0
#define ABORT_TX        		0x10
#define MODE_ONESHOT		0x08
#define CLKOUT_ENABLE		0x04
#define CLKOUT_DISABLE		0x00
#define CLKOUT_PS1			0x00
#define CLKOUT_PS2			0x01
#define CLKOUT_PS4			0x02
#define CLKOUT_PS8			0x03


/*CNF1 Register Values*/

#define SJW1            0x00
#define SJW2            0x40
#define SJW3            0x80
#define SJW4            0xC0


/*CNF2 Register Values*/

#define BTLMODE			0x80
#define SAMPLE_1X       	0x00
#define SAMPLE_3X       	0x40


/*CNF3 Register Values*/

#define SOF_ENABLE		0x80
#define SOF_DISABLE		0x00
#define WAKFIL_ENABLE	0x40
#define WAKFIL_DISABLE	0x00


/*CANINTF Register Bits*/

#define MCP_RX0IF		0x01
#define MCP_RX1IF		0x02
#define MCP_TX0IF		0x04
#define MCP_TX1IF		0x08
#define MCP_TX2IF		0x10
#define MCP_ERRIF		0x20
#define MCP_WAKIF		0x40
#define MCP_MERRF		0x80



#define MCP2510_OK       	  (0)
#define MCP2510_FAIL    	  (1)
#define MCP_ALLTXBUSY       (2)


#define CANDEFAULTIDENT    (0x55CC)

#define CAN_OK         		(0)
#define CAN_FAILINIT   	(1)
#define CAN_FAILTX     	(2)
#define CAN_MSGAVAIL   	(3)
#define CAN_NOMSG      	(4)
#define CAN_CTRLERROR  	(5)
#define CAN_FAIL       		(0xff)



#define MCP_16MHZ_1MBPS_CFG1		0x00
#define MCP_16MHZ_1MBPS_CFG2		0x82
#define MCP_16MHZ_1MBPS_CFG3		0x02
		
#define MCP_16MHZ_800KBPS_CFG1		0x00
#define MCP_16MHZ_800KBPS_CFG2		0x92
#define MCP_16MHZ_800KBPS_CFG3		0x02
	     
#define MCP_16MHZ_500KBPS_CFG1		0x00	
#define MCP_16MHZ_500KBPS_CFG2		0x9e
#define MCP_16MHZ_500KBPS_CFG3		0x03
              
#define MCP_16MHZ_250KBPS_CFG1		0x01	
#define MCP_16MHZ_250KBPS_CFG2		0x1e
#define MCP_16MHZ_250KBPS_CFG3		0x03
                   
#define MCP_16MHZ_125KBPS_CFG1		0x03
#define MCP_16MHZ_125KBPS_CFG2		0x9e
#define MCP_16MHZ_125KBPS_CFG3		0x03


/*spi write txbuffer command*/
#define CMD_TXB0SIDH		0x40
#define CMD_TXB0D0			0x41
#define CMD_TXB1SIDH		0x42
#define CMD_TXB1D0			0x43
#define CMD_TXB2SIDH		0x44
#define CMD_TXB2D0			0x45

/*spi RTS command*/
#define CMD_TX0RTS			0x81
#define CMD_TX1RTS			0x82
#define CMD_TX2RTS			0x84

/*spi read rx buffer command*/
#define CMD_RXB0SIDH		0x61  /*0x90*/    /** 0x61  */
#define CMD_RXB0D0			0x66 /*0x92*/    /** 0x66  */
#define CMD_RXB1SIDH		0x71 /*0x94*/   /** 0x71 */
#define CMD_RXB1D0			0x76 /*0x96*/    /** 0x76  */









/*======================END===================================*/






STATUS CANInit(int channel,CANInitInBuf canCfg);
STATUS CANSend(int channel, CANMSG CanSmsg);
STATUS CANReceive(int channel, CANMSG *CanRmsg);
#endif /* __INCvxbSpiMCP2510h */
