/*
 * WangXun 1 Gigabit PCI Express End Driver
 * Copyright (c) 2015 - 2017 Beijing WangXun Technology Co., Ltd.
 */

#include <vxWorks.h>
#include <intLib.h>
#include <logLib.h>
#include <muxLib.h>
#include <netLib.h>
#include <netBufLib.h>
#include <semLib.h>
#include <sysLib.h>
#include <vxBusLib.h>
#include <wdLib.h>
#include <etherMultiLib.h>
#include <end.h>
#define END_MACROS
#include <endLib.h>
#include <endMedia.h>
#include <vxAtomicLib.h>
#ifndef NGB_VXB_DMA_BUF
#include <cacheLib.h>
#endif

#include <hwif/vxbus/vxBus.h>
#include <hwif/vxbus/hwConf.h>
#include <hwif/vxbus/vxbPciLib.h>
#include <hwif/util/vxbDmaBufLib.h>
#include <hwif/util/vxbParamSys.h>
#include <../src/hwif/h/vxbus/vxbAccess.h>
#include <../src/hwif/h/hEnd/hEnd.h>

#include <drv/pci/pciConfigLib.h>

#include <vxbNgbeEnd.h>

/* Add for ngbprint */

VXB_DEVICE_ID pDev0;
VXB_DEVICE_ID pDev1;
VXB_DEVICE_ID pDev2;
VXB_DEVICE_ID pDev3;


IMPORT FUNCPTR _func_m2PollStatsIfPoll;

/* VxBus methods */

LOCAL void     ngbInstInit (VXB_DEVICE_ID);
LOCAL void     ngbInstInit2 (VXB_DEVICE_ID);
LOCAL void     ngbInstConnect (VXB_DEVICE_ID);
LOCAL STATUS   ngbInstUnlink (VXB_DEVICE_ID, void *);

/* mux methods */

LOCAL void     ngbMuxConnect (VXB_DEVICE_ID, void *);

LOCAL struct drvBusFuncs ngbFuncs =
    {
    ngbInstInit,	/* devInstanceInit */
    ngbInstInit2,	/* devInstanceInit2 */
    ngbInstConnect	/* devConnect */
    };

LOCAL struct vxbDeviceMethod ngbMethods[] =
    {
    DEVMETHOD(muxDevConnect, ngbMuxConnect),
    DEVMETHOD(vxbDrvUnlink,  ngbInstUnlink),
    { 0, 0 }
    };

/*
 * List of supported device IDs.
 */
LOCAL struct vxbPciID ngbPciDevIDList[] =
    {
        /* { devID, vendID } */
        { NGB_DEV_ID_EM_TEST, PCI_VENDOR_ID_TRUSTNETIC },
        { NGB_DEV_ID_EM_WX1860AL_W, PCI_VENDOR_ID_TRUSTNETIC },
        { NGB_DEV_ID_EM_WX1860A2, PCI_VENDOR_ID_TRUSTNETIC },
        { NGB_DEV_ID_EM_WX1860A2S, PCI_VENDOR_ID_TRUSTNETIC },
        { NGB_DEV_ID_EM_WX1860A4, PCI_VENDOR_ID_TRUSTNETIC },
        { NGB_DEV_ID_EM_WX1860A4S, PCI_VENDOR_ID_TRUSTNETIC },
        { NGB_DEV_ID_EM_WX1860AL2, PCI_VENDOR_ID_TRUSTNETIC },
        { NGB_DEV_ID_EM_WX1860AL2S, PCI_VENDOR_ID_TRUSTNETIC },
        { NGB_DEV_ID_EM_WX1860AL4, PCI_VENDOR_ID_TRUSTNETIC },
        { NGB_DEV_ID_EM_WX1860AL4S, PCI_VENDOR_ID_TRUSTNETIC },
        { NGB_DEV_ID_EM_WX1860A1, PCI_VENDOR_ID_TRUSTNETIC},
        { NGB_DEV_ID_EM_WX1860AL1, PCI_VENDOR_ID_TRUSTNETIC}
    };

/* default queue parameters */
LOCAL HEND_RX_QUEUE_PARAM ngbRxQueueDefault = {
    NULL,                       /* jobQueId */
    0,                          /* priority */
    0,                          /* rbdNum */
    0,                          /* rbdTupleRatio */
    0,                          /* rxBufSize */
    NULL,                       /* pBufMemBase */
    0,                          /* rxBufMemSize */
    0,                          /* rxBufMemAttributes */
    NULL,                       /* rxBufMemFreeMethod */
    NULL,                       /* pRxBdBase */
    0,                          /* rxBdMemSize */
    0,                          /* rxBdMemAttributes */
    NULL                        /* rxBdMemFreeMethod */
};

LOCAL HEND_TX_QUEUE_PARAM ngbTxQueueDefault = {
    NULL,                       /* jobQueId */
    0,                          /* priority */
    0,                          /* tbdNum */
    0,                          /* allowedFrags */
    NULL,                       /* pTxBdBase */
    0,                          /* txBdMemSize */
    0,                          /* txBdMemAttributes */
    NULL                        /* txBdMemFreeMethod */
};

LOCAL VXB_PARAMETERS ngbParamDefaults[] =
    {
        {"rxQueue00", VXB_PARAM_POINTER, {(void *)&ngbRxQueueDefault}},
        {"txQueue00", VXB_PARAM_POINTER, {(void *)&ngbTxQueueDefault}},
        {"jumboEnable", VXB_PARAM_INT32, {(void *)0}},
        {NULL, VXB_PARAM_END_OF_LIST, {NULL}}
    };

LOCAL struct vxbPciRegister ngbDevPciRegistration =
    {
        {
        NULL,                   /* pNext */
        VXB_DEVID_DEVICE,       /* devID */
        VXB_BUSID_PCI,          /* busID = PCI */
#ifdef VXB_VER_4_0_0
        VXB_VER_4_0_0,          /* vxbVersion */
#else
        VXBUS_VERSION_3,        /* vxbVersion */
#endif
        NGB_NAME,               /* drvName */
        &ngbFuncs,              /* pDrvBusFuncs */
        ngbMethods,             /* pMethods */
        NULL,                   /* devProbe */
        ngbParamDefaults        /* pParamDefaults */
        },
    NELEMENTS(ngbPciDevIDList),
    ngbPciDevIDList
    };

/* Driver utility functions */

LOCAL STATUS    ngbReset (VXB_DEVICE_ID);
LOCAL void      ngbIvarWxSet (VXB_DEVICE_ID);

/* Phy methods */

LOCAL int       ngbPhyRead (VXB_DEVICE_ID, UINT32, UINT32, UINT16 *);
LOCAL int       ngbPhyWrite (VXB_DEVICE_ID, UINT32, UINT32, UINT16);

LOCAL int       ngbPhyInit (VXB_DEVICE_ID);
LOCAL int       ngbPhySetupOnce (VXB_DEVICE_ID);
LOCAL int       ngbSetupLinkInternal (VXB_DEVICE_ID, UINT32);
LOCAL BOOL      ngbLinkIsUp (VXB_DEVICE_ID);

/* END functions */

LOCAL END_OBJ * ngbEndLoad (char *, void *);
LOCAL STATUS    ngbEndUnload (END_OBJ *);
LOCAL int       ngbEndIoctl (END_OBJ *, int, caddr_t);
LOCAL STATUS    ngbEndMCastAddrAdd (END_OBJ *, char *);
LOCAL STATUS    ngbEndMCastAddrDel (END_OBJ *, char *);
LOCAL STATUS    ngbEndMCastAddrGet (END_OBJ *, MULTI_TABLE *);
LOCAL void      ngbEndHashTblPopulate (NGB_DRV_CTRL *);
LOCAL STATUS    ngbEndStatsDump (NGB_DRV_CTRL *);
LOCAL void      ngbEndRxConfig (NGB_DRV_CTRL *);
LOCAL STATUS    ngbEndStart (END_OBJ *);
LOCAL STATUS    ngbEndStop (END_OBJ *);
LOCAL int       ngbEndSend (END_OBJ *, M_BLK_ID );
LOCAL void      ngbEndTbdClean (NGB_DRV_CTRL *);
LOCAL int       ngbEndEncap (NGB_DRV_CTRL *, M_BLK_ID );
LOCAL STATUS    ngbEndPollSend (END_OBJ *, M_BLK_ID );
LOCAL int       ngbEndPollReceive (END_OBJ *, M_BLK_ID );
LOCAL void      ngbEndInt (NGB_DRV_CTRL *);
LOCAL int       ngbEndRxHandle (NGB_DRV_CTRL *);
LOCAL void      ngbEndTxHandle (NGB_DRV_CTRL *);
LOCAL void      ngbEndIntHandle (void *);

LOCAL NET_FUNCS ngbNetFuncs =
    {
    ngbEndStart,                        /* start func. */
    ngbEndStop,                         /* stop func. */
    ngbEndUnload,                       /* unload func. */
    ngbEndIoctl,                        /* ioctl func. */
    ngbEndSend,                         /* send func. */
    ngbEndMCastAddrAdd,                 /* multicast add func. */
    ngbEndMCastAddrDel,                 /* multicast delete func. */
    ngbEndMCastAddrGet,                 /* multicast get fun. */
    ngbEndPollSend,                     /* polling send func. Maybe unavail */
    ngbEndPollReceive,                  /* polling receive func. Maybe unavail */
    endEtherAddressForm,                /* put address info into a NET_BUFFER */
    endEtherPacketDataGet,              /* get pointer to data in NET_BUFFER */
    endEtherPacketAddrGet               /* Get packet addresses */
    };

/*****************************************************************************
*
* ngbRegister - register with the VxBus subsystem
*
* This routine registers the wangxun txgbe driver with VxBus as a
* child of the PCI bus type.
*
* RETURNS: N/A
*
* ERRNO: N/A
*/

void ngbRegister
    (
    void
    )
    {
    vxbDevRegister((struct vxbDevRegInfo *)&ngbDevPciRegistration);
    return;
    }

/*****************************************************************************
*
* ngbInstInit - VxBus instInit handler
*
* This function implements the VxBus instInit handler for an ngb
* device instance. The only thing done here is to select a unit
* number for the device.
*
* RETURNS: N/A
*
* ERRNO: N/A
*/

LOCAL void ngbInstInit
    (
    VXB_DEVICE_ID pDev
    )
    {
    vxbNextUnitGet (pDev);
    return;
    }

/*****************************************************************************
*
* ngbInstInit2 - VxBus instInit2 handler
*
* This function implements the VxBus instInit2 handler for an ngb
* device instance. Once we reach this stage of initialization, it's
* safe for us to allocate memory, so we can create our pDrvCtrl
* structure and do some initial hardware setup. The important
* steps we do here are to connect our ISR to our assigned interrupt
* vector, read the station address from the EEPROM, and set up our
* vxbDma tags and memory regions.
*
* RETURNS: N/A
*
* ERRNO: N/A
*/

LOCAL void ngbInstInit2
    (
    VXB_DEVICE_ID pDev
    )
    {
    NGB_DRV_CTRL * pDrvCtrl;
    VXB_INST_PARAM_VALUE val;
    UINT32 media;
    UINT32 rar_high;
    UINT32 rar_low;
    int i;

    pDrvCtrl = malloc (sizeof(NGB_DRV_CTRL));
    if (pDrvCtrl == NULL)
        {
        logMsg("ngbInstInit2:malloc error.\n",0,1,2,3,4,5);
        return;
        }

    bzero ((char *)pDrvCtrl, sizeof(NGB_DRV_CTRL));
    pDev->pDrvCtrl = pDrvCtrl;
    pDrvCtrl->ngbDev = pDev;

    for (i = 0; i < VXB_MAXBARS; i++)
        {
        if (pDev->regBaseFlags[i] == VXB_REG_MEM)
            break;
        }

    /* Should never happen. */

    if (i == VXB_MAXBARS)
        {
        DBG_LOG("get VXB_REG_MEM failed\n", 0,0,0,0,0,0);
        return;
        }

    vxbRegMap (pDev, i, &pDrvCtrl->ngbHandle);
    pDrvCtrl->ngbBar = pDev->pRegBase[i];

    if (pDev->unitNumber == 0)
        pDev0 = pDev;
    else if (pDev->unitNumber == 1)
        pDev1 = pDev;
    else if (pDev->unitNumber == 2)
        pDev2 = pDev;
    else if (pDev->unitNumber == 3)
        pDev3 = pDev;

    /* create mutex to protect critical sections */

    pDrvCtrl->ngbDevSem = semMCreate (SEM_Q_PRIORITY |
        SEM_DELETE_SAFE | SEM_INVERSION_SAFE);

#ifdef VXB_VER_4_0_0
    VXB_PCI_BUS_CFG_READ (pDev, PCI_CFG_DEVICE_ID, 2, pDrvCtrl->ngbDevId);
#else
    UINT32 flags;
    pDev->pAccess->busCfgRead (pDev, PCI_CFG_DEVICE_ID, 2,
        (char *)&pDrvCtrl->ngbDevId, &flags);
#endif

    if (pDrvCtrl->ngbDevId == NGB_DEV_ID_EM_TEST ||
        pDrvCtrl->ngbDevId == NGB_DEV_ID_EM_WX1860A2 ||
        pDrvCtrl->ngbDevId == NGB_DEV_ID_EM_WX1860A2S ||
        pDrvCtrl->ngbDevId == NGB_DEV_ID_EM_WX1860A4 ||
        pDrvCtrl->ngbDevId == NGB_DEV_ID_EM_WX1860A4S ||
        pDrvCtrl->ngbDevId == NGB_DEV_ID_EM_WX1860AL2 ||
        pDrvCtrl->ngbDevId == NGB_DEV_ID_EM_WX1860AL2S ||
        pDrvCtrl->ngbDevId == NGB_DEV_ID_EM_WX1860AL4 ||
        pDrvCtrl->ngbDevId == NGB_DEV_ID_EM_WX1860AL4S ||
        pDrvCtrl->ngbDevId == NGB_DEV_ID_EM_WX1860A1 ||
        pDrvCtrl->ngbDevId == NGB_DEV_ID_EM_WX1860AL1 ||
        pDrvCtrl->ngbDevId == NGB_DEV_ID_EM_WX1860AL_W )
        {
        pDrvCtrl->ngbDevType = NGB_DEVTYPE_COPPER;
        pDrvCtrl->phy_type = ngb_phy_internal;
        }

    /* Get the station address */
    CSR_WRITE_4(pDev, NGB_PSR_MAC_SWC_IDX, 0);

    rar_high = CSR_READ_4(pDev, NGB_PSR_MAC_SWC_AD_H);
    rar_low = CSR_READ_4(pDev, NGB_PSR_MAC_SWC_AD_L);

    for (i = 0; i < 2; i++)
        pDrvCtrl->ngbAddr[i] = (UINT8)(rar_high >> (1 - i) * 8);

    for (i = 0; i < 4; i++)
        pDrvCtrl->ngbAddr[i + 2] = (UINT8)(rar_low >> (3 - i) * 8);

    ngbReset (pDev);

    /* Media support */
    switch (pDrvCtrl->ngbDevType)
        {
        case NGB_DEVTYPE_COPPER:
            media = IFM_1000_T;
            break;
        case NGB_DEVTYPE_FIBER:
            media = IFM_1000_LX;
            break;
        case NGB_DEVTYPE_OTHER:
            media = IFM_1000_T;
            break;
        default:
            media = IFM_1000_T;
            break;
        }

    /*
     * We don't bother using miiBus for this adapter, since it
     * doesn't really have an MII-compatible link management interface.
     * Also, there's really only one supported mode.
     */

    pDrvCtrl->ngbCurStatus = IFM_AVALID;
    pDrvCtrl->ngbCurMedia = IFM_ETHER | IFM_FDX | media;
    pDrvCtrl->ngbMediaList = malloc(sizeof(END_MEDIALIST) + sizeof(UINT32));
    pDrvCtrl->ngbMediaList->endMediaList[0] = pDrvCtrl->ngbCurMedia;
    pDrvCtrl->ngbMediaList->endMediaListLen = 1;
    pDrvCtrl->ngbMediaList->endMediaListDefault = pDrvCtrl->ngbCurMedia;

    i = vxbInstParamByNameGet (pDev, "jumboEnable", VXB_PARAM_INT32, &val);

    if (i != OK || val.int32Val == 0)
        {
        i = NGB_CLSIZE;
        pDrvCtrl->ngbMaxMtu = NGB_MTU;
        }
    else
        {
        i = END_JUMBO_CLSIZE;
        pDrvCtrl->ngbMaxMtu = NGB_JUMBO_MTU;
        }

#ifdef NGB_VXB_DMA_BUF
    pDrvCtrl->ngbParentTag = vxbDmaBufTagParentGet (pDev, 0);

    /* Create tag for RX descriptor ring. */

    pDrvCtrl->ngbRxDescTag = vxbDmaBufTagCreate (pDev,
        pDrvCtrl->ngbParentTag,                    /* parent */
        256            /*_CACHE_ALIGN_SIZE*/,      /* alignment */
        0,                                         /* boundary */
        0xFFFFFFFF,                                /* lowaddr */
        0xFFFFFFFF,                                /* highaddr */
        NULL,                                      /* filter */
        NULL,                                      /* filterarg */
        sizeof(NGB_ADV_RDESC) * NGB_RX_DESC_CNT,   /* max size */
        1,                                         /* nSegments */
        sizeof(NGB_ADV_RDESC) * NGB_RX_DESC_CNT,   /* max seg size */
        VXB_DMABUF_ALLOCNOW|VXB_DMABUF_NOCACHE,    /* flags */
        NULL,                                      /* lockfunc */
        NULL,                                      /* lockarg */
        NULL);                                     /* ppDmaTag */

    pDrvCtrl->ngbRxDescMem = vxbDmaBufMemAlloc (pDev,
        pDrvCtrl->ngbRxDescTag, NULL, 0, &pDrvCtrl->ngbRxDescMap);

    /* Create tag for TX descriptor ring. */

    pDrvCtrl->ngbTxDescTag = vxbDmaBufTagCreate (pDev,
        pDrvCtrl->ngbParentTag,                    /* parent */
        256 /*_CACHE_ALIGN_SIZE*/,                 /* alignment */
        0,                                         /* boundary */
        0xFFFFFFFF,                                /* lowaddr */
        0xFFFFFFFF,                                /* highaddr */
        NULL,                                      /* filter */
        NULL,                                      /* filterarg */
        sizeof(NGB_ADV_TDESC) * NGB_TX_DESC_CNT,   /* max size */
        1,                                         /* nSegments */
        sizeof(NGB_ADV_TDESC) * NGB_TX_DESC_CNT,   /* max seg size */
        VXB_DMABUF_ALLOCNOW|VXB_DMABUF_NOCACHE,    /* flags */
        NULL,                                      /* lockfunc */
        NULL,                                      /* lockarg */
        NULL);                                     /* ppDmaTag */

    pDrvCtrl->ngbTxDescMem = vxbDmaBufMemAlloc (pDev,
        pDrvCtrl->ngbTxDescTag, NULL, 0, &pDrvCtrl->ngbTxDescMap);

    /* setup isb resources */
    pDrvCtrl->ngbIsbTag = vxbDmaBufTagCreate (pDev,
        pDrvCtrl->ngbParentTag,                    /* parent */
        256 /*_CACHE_ALIGN_SIZE*/,                 /* alignment */
        0,                                         /* boundary */
        0xFFFFFFFF,                                /* lowaddr */
        0xFFFFFFFF,                                /* highaddr */
        NULL,                                      /* filter */
        NULL,                                      /* filterarg */
        sizeof(UINT32) * NGB_ISB_MAX,            /* max size */
        1,                                         /* nSegments */
        sizeof(UINT32) * NGB_ISB_MAX,            /* max seg size */
        VXB_DMABUF_ALLOCNOW|VXB_DMABUF_NOCACHE,    /* flags */
        NULL,                                      /* lockfunc */
        NULL,                                      /* lockarg */
        NULL);                                     /* ppDmaTag */

    pDrvCtrl->isb_mem = vxbDmaBufMemAlloc (pDev,
        pDrvCtrl->ngbIsbTag, NULL, 0, &pDrvCtrl->ngbIsbMap);

    pDrvCtrl->ngbMblkTag = vxbDmaBufTagCreate (pDev,
        pDrvCtrl->ngbParentTag,                    /* parent */
        1,                                         /* alignment */
        0,                                         /* boundary */
        0xFFFFFFFF,                                /* lowaddr */
        0xFFFFFFFF,                                /* highaddr */
        NULL,                                      /* filter */
        NULL,                                      /* filterarg */
        i,                                         /* max size */
        NGB_MAXFRAG,                               /* nSegments */
        i,                                         /* max seg size */
        VXB_DMABUF_ALLOCNOW,                       /* flags */
        NULL,                                      /* lockfunc */
        NULL,                                      /* lockarg */
        NULL);                                     /* ppDmaTag */

    for (i = 0; i < NGB_TX_DESC_CNT; i++)
        {
        if (vxbDmaBufMapCreate (pDev, pDrvCtrl->ngbMblkTag, 0,
                    &pDrvCtrl->ngbTxMblkMap[i]) == NULL)
        logMsg("create Tx map %d failed\n", i, 0, 0, 0, 0, 0);
        }

    for (i = 0; i < NGB_RX_DESC_CNT; i++)
        {
        if (vxbDmaBufMapCreate (pDev, pDrvCtrl->ngbMblkTag, 0,
                    &pDrvCtrl->ngbRxMblkMap[i]) == NULL)
        logMsg("create Rx map %d failed\n", i, 0, 0, 0, 0, 0);
        }

#else
    /*
     * Note, cacheDmaMalloc() is guaranteed to return a cache-aligned
     * buffer only when the cache is enabled (and it may not be in bootroms).
     * So we must do manual alignment.  The descriptor base address must
     * be at least 16-byte aligned, but the memory length must be a multiple
     * of 128 for both RX and TX; so we might as well make the start of the
     * descriptor table 128-byte aligned anyway.
     *
     * We introduce ngbDescBuf to hold the (possibly) unaligned
     * start address, for freeing.
     */

    if ((pDrvCtrl->ngbDescBuf =
        cacheDmaMalloc (sizeof(NGB_ADV_RDESC) * NGB_RX_DESC_CNT +
                 sizeof(NGB_ADV_TDESC) * NGB_TX_DESC_CNT + 128))
        == NULL)
        logMsg (NGB_NAME "%d: could not allocate descriptor memory\n",
            pDev->unitNumber, 0, 0, 0, 0, 0);
    if ((pDrvCtrl->ngbIsbBuf =
        cacheDmaMalloc (sizeof(UINT32) * NGB_ISB_MAX + 128))
        == NULL)
        logMsg (NGB_NAME "%d: could not allocate isb memory\n",
            pDev->unitNumber, 0, 0, 0, 0, 0);

    pDrvCtrl->ngbRxDescMem = (NGB_ADV_RDESC *)
        ROUND_UP (pDrvCtrl->ngbDescBuf, 128);
    pDrvCtrl->ngbTxDescMem = (NGB_ADV_TDESC *)
        (pDrvCtrl->ngbRxDescMem + NGB_RX_DESC_CNT);
    pDrvCtrl->isb_mem = (UINT32 *)
        ROUND_UP (pDrvCtrl->ngbIsbBuf, 128);
#endif

    return;
    }

/*****************************************************************************
*
* ngbInstConnect -  VxBus instConnect handler
*
* This function implements the VxBus instConnect handler for an ngb
* device instance.
*
* RETURNS: N/A
*
* ERRNO: N/A
*/

LOCAL void ngbInstConnect
    (
    VXB_DEVICE_ID pDev
    )
    {
    return;
    }

/*****************************************************************************
*
* ngbInstUnlink - VxBus unlink handler
*
* This function shuts down an ngb device instance in response to an
* unlink event from VxBus. This may occur if our VxBus instance has
* been terminated, or if the ngb driver has been unloaded. When an
* unlink event occurs, we must shut down and unload the END interface
* associated with this device instance and then release all the
* resources allocated during instance creation, such as vxbDma
* memory and maps, and interrupt handles.
*
* RETURNS: OK if device was successfully destroyed, otherwise ERROR
*
* ERRNO: N/A
*/

LOCAL STATUS ngbInstUnlink
    (
    VXB_DEVICE_ID pDev,
    void * unused
    )
    {
    NGB_DRV_CTRL * pDrvCtrl;

#ifdef NGB_VXB_DMA_BUF
    int i;
#endif

    pDrvCtrl = pDev->pDrvCtrl;

    /*
     * Stop the device and detach from the MUX.
     * Note: it's possible someone might try to delete
     * us after our vxBus instantiation has completed,
     * but before anyone has called our muxConnect method.
     * In this case, there'll be no MUX connection to
     * tear down, so we can skip this step.
     */

    if (pDrvCtrl->ngbMuxDevCookie != NULL)
        {
        if (muxDevStop (pDrvCtrl->ngbMuxDevCookie) != OK)
            return (ERROR);

    /* Detach from the MUX. */

        if (muxDevUnload (NGB_NAME, pDev->unitNumber) != OK)
            return (ERROR);
        }

    /* Release the memory we allocated for the DMA rings */
#ifdef NGB_VXB_DMA_BUF

    vxbDmaBufMemFree (pDrvCtrl->ngbRxDescTag, pDrvCtrl->ngbRxDescMem,
        pDrvCtrl->ngbRxDescMap);

    vxbDmaBufMemFree (pDrvCtrl->ngbTxDescTag, pDrvCtrl->ngbTxDescMem,
        pDrvCtrl->ngbTxDescMap);

    vxbDmaBufMemFree (pDrvCtrl->ngbIsbTag, pDrvCtrl->isb_mem,
        pDrvCtrl->ngbIsbMap);

    for (i = 0; i < NGB_RX_DESC_CNT; i++)
        vxbDmaBufMapDestroy (pDrvCtrl->ngbMblkTag,
            pDrvCtrl->ngbRxMblkMap[i]);

    for (i = 0; i < NGB_TX_DESC_CNT; i++)
        vxbDmaBufMapDestroy (pDrvCtrl->ngbMblkTag,
            pDrvCtrl->ngbTxMblkMap[i]);

    /* Destroy the tags. */

    vxbDmaBufTagDestroy (pDrvCtrl->ngbRxDescTag);
    vxbDmaBufTagDestroy (pDrvCtrl->ngbTxDescTag);
    vxbDmaBufTagDestroy (pDrvCtrl->ngbIsbTag);
    vxbDmaBufTagDestroy (pDrvCtrl->ngbMblkTag);
#else
    cacheDmaFree (pDrvCtrl->ngbDescBuf);
    cacheDmaFree (pDrvCtrl->ngbIsbBuf);
#endif

    /* Disconnect the ISR. */

    vxbIntDisconnect (pDev, 0, ngbEndInt, pDrvCtrl);

    semDelete (pDrvCtrl->ngbDevSem);

    /* Destroy the adapter context. */
    free (pDrvCtrl);
    pDev->pDrvCtrl = NULL;

    return (OK);
}

/************************************************************************
*ngbPhyRead() - phy read method
*
*For internal phy only
*
*/
LOCAL int ngbPhyRead
    (
    VXB_DEVICE_ID pDev,
    UINT32 reg_offset,
    UINT32 page,
    UINT16 *phy_data
    )
    {
    BOOL page_select = FALSE;
    /* clear input */

    *phy_data = 0;

    if (0 != page)
        {
        /* select page */
        if (0xa42 == page || 0xa43 == page || 0xa46 == page || 0xa47 == page || 0xd04 == page)
            {
            CSR_WRITE_4(pDev, NGB_PHY_CONFIG(NGB_INTERNAL_PHY_PAGE_SELECT_OFFSET), page);
            page_select = TRUE;
            }
        }

    if (reg_offset >= NGB_INTERNAL_PHY_OFFSET_MAX)
        {
        return 0;
        }

    *phy_data = 0xFFFF & CSR_READ_4(pDev, NGB_PHY_CONFIG(reg_offset));

    if (page_select)
        {
        CSR_WRITE_4(pDev, NGB_PHY_CONFIG(NGB_INTERNAL_PHY_PAGE_SELECT_OFFSET), 0);
        }

    return 0;

    }

/************************************************************************
*ngbPhyWrite() - phy write method
*
*For internal phy only
*
*/
LOCAL int ngbPhyWrite
    (
    VXB_DEVICE_ID pDev,
    UINT32 reg_offset,
    UINT32 page,
    UINT16 phy_data
    )
    {
    BOOL page_select = FALSE;

    if (0 != page)
        {
        /* select page */
        if (0xa42 == page || 0xa43 == page || 0xa46 == page || 0xa47 == page || 0xd04 == page)
            {
            CSR_WRITE_4(pDev, NGB_PHY_CONFIG(NGB_INTERNAL_PHY_PAGE_SELECT_OFFSET), page);
            page_select = TRUE;
            }
        }

    if (reg_offset >= NGB_INTERNAL_PHY_OFFSET_MAX)
        {
        return 0;
        }

    CSR_WRITE_4(pDev, NGB_PHY_CONFIG(reg_offset), phy_data);

    if (page_select)
        {
        CSR_WRITE_4(pDev, NGB_PHY_CONFIG(NGB_INTERNAL_PHY_PAGE_SELECT_OFFSET), 0);
        }

    return 0;
    }

/*****************************************************************************
*
* ngbLinkUpdate - link change event handler
*
* This function processes link change event notifications triggered by
* the interrupt handler. In most configurations, the link speed will
* remain constant at 10Gbps full duplex, but we must respond to cable
* unplug and replug events.
*
* Once we determine the new link state, we will announce the change
* to any bound protocols via muxError(). We also update the ifSpeed
* fields in the MIB2 structures so that SNMP queries can detect the
* correct link speed.
*
* RETURNS: ERROR if obtaining the new media setting fails, else OK
*
* ERRNO: N/A
*/

LOCAL STATUS ngbLinkUpdate
    (
    VXB_DEVICE_ID pDev
    )
    {
    NGB_DRV_CTRL * pDrvCtrl;
    UINT32 oldStatus;

    if (pDev->pDrvCtrl == NULL)
        {
        return (ERROR);
        }

    pDrvCtrl = (NGB_DRV_CTRL *)pDev->pDrvCtrl;

    semTake (pDrvCtrl->ngbDevSem, WAIT_FOREVER);

    /* Check link status */
    oldStatus = pDrvCtrl->ngbCurStatus;

    if (ngbLinkIsUp(pDev))
        {
        pDrvCtrl->ngbCurStatus = IFM_AVALID|IFM_ACTIVE;
        }
    else
        {
        pDrvCtrl->ngbCurStatus = IFM_AVALID;
        }

    if (pDrvCtrl->ngbCurStatus & IFM_ACTIVE)
        {
        CSR_WRITE_4(pDev, NGB_MAC_TX_CFG, NGB_MAC_TX_CFG_TE | NGB_MAC_TX_CFG_SPEED_1G);
        CSR_WRITE_4(pDev, NGB_MAC_RX_CFG, CSR_READ_4(pDev, NGB_MAC_RX_CFG));
        CSR_WRITE_4(pDev, NGB_MAC_PKT_FLT, NGB_MAC_PKT_FLT_PR);
        CSR_WRITE_4(pDev, NGB_MAC_WDG_TIMEOUT,CSR_READ_4(pDev, NGB_MAC_WDG_TIMEOUT));
        }

    /* If status went from down to up, announce link up. */

    if (pDrvCtrl->ngbCurStatus & IFM_ACTIVE && !(oldStatus & IFM_ACTIVE))
        {
        jobQueueStdPost (pDrvCtrl->ngbJobQueue, NET_TASK_QJOB_PRI,
                 muxLinkUpNotify, &pDrvCtrl->ngbEndObj,
                 NULL, NULL, NULL, NULL);
        }

    /* If status went from up to down, announce link down. */

    if (!(pDrvCtrl->ngbCurStatus & IFM_ACTIVE) && oldStatus & IFM_ACTIVE)
        {
        jobQueueStdPost (pDrvCtrl->ngbJobQueue, NET_TASK_QJOB_PRI,
                 muxLinkDownNotify, &pDrvCtrl->ngbEndObj,
                 NULL, NULL, NULL, NULL);
        }

    semGive (pDrvCtrl->ngbDevSem);
    return (OK);
    }

/*****************************************************************************
*
* ngbMuxConnect - muxConnect method handler
*
* This function handles muxConnect() events, which may be triggered
* manually or (more likely) by the bootstrap code. Most VxBus
* initialization occurs before the MUX has been fully initialized,
* so the usual muxDevLoad()/muxDevStart() sequence must be defered
* until the networking subsystem is ready. This routine will ultimately
* trigger a call to ngbEndLoad() to create the END interface instance.
*
* RETURNS: N/A
*
* ERRNO: N/A
*/

LOCAL void ngbMuxConnect
    (
    VXB_DEVICE_ID pDev,
    void * unused
    )
    {
    NGB_DRV_CTRL *pDrvCtrl;

    /*
     * Attach our ISR. For PCI, the index value is always
     * 0, since the PCI bus controller dynamically sets
     * up interrupts for us.
     */
    vxbIntConnect (pDev, 0, ngbEndInt, pDev->pDrvCtrl);
    pDrvCtrl = pDev->pDrvCtrl;

    /* Save the cookie. */
    pDrvCtrl->ngbMuxDevCookie = muxDevLoad (pDev->unitNumber,
        ngbEndLoad, "", TRUE, pDev);

    if (pDrvCtrl->ngbMuxDevCookie != NULL)
        {
        muxDevStart (pDrvCtrl->ngbMuxDevCookie);
        }

    if (_func_m2PollStatsIfPoll != NULL)
        {
        endPollStatsInit (pDrvCtrl->ngbMuxDevCookie, _func_m2PollStatsIfPoll);
        }

    return;
    }

/*****************************************************************************
*
* ngbReset - reset the wangxun ngbe controller
*
*/
LOCAL STATUS ngbReset
    (
    VXB_DEVICE_ID pDev
    )
    {
    NGB_DRV_CTRL * pDrvCtrl;
    UINT32 lan_id = 0;

    pDrvCtrl = pDev->pDrvCtrl;

    lan_id = (CSR_READ_4(pDev, NGB_CFG_PORT_ST) & 0x00000300U) >>8;

    if (lan_id == 0)
        {
        CSR_SETBIT_4(pDev, NGB_MIS_RST, NGB_MIS_RST_LAN0_RST);
        }
    else if (lan_id == 1)
        {
        CSR_SETBIT_4(pDev, NGB_MIS_RST, NGB_MIS_RST_LAN1_RST);
        }
    else if (lan_id == 2) 
        {
        CSR_SETBIT_4(pDev, NGB_MIS_RST, NGB_MIS_RST_LAN2_RST);
        }
    else if (lan_id == 3)
        {
        CSR_SETBIT_4(pDev, NGB_MIS_RST, NGB_MIS_RST_LAN3_RST);
        }

    vxbMsDelay(100);

    CSR_READ_4(pDev, NGB_MIS_PWR);

    return (OK);
}

/*****************************************************************************
*
* ngbPhySetupOnce - internal phy setup method
*
*/
LOCAL int ngbPhySetupOnce
    (
    VXB_DEVICE_ID pDev
    )
    {
    NGB_DRV_CTRL * pDrvCtrl;

    pDrvCtrl = pDev->pDrvCtrl;

    UINT16 value = 0;

    /* Phy Config */
    CSR_WRITE_4(pDev, NGB_MDIO_CLAUSE22, 0xF);

    if (pDrvCtrl->phy_type ==ngb_phy_internal)
        {
        value = NGB_INTPHY_INT_LSC | NGB_INTPHY_INT_ANC;
        ngbPhyWrite(pDev, 0x12, 0xa42, value);
        }

    ngbPhyInit(pDev);

    ngbSetupLinkInternal(pDev, NGB_LINK_SPEED_1GB_FULL |
                               NGB_LINK_SPEED_100_FULL |
                               NGB_LINK_SPEED_10_FULL);

    return 0;
    }

/************************************************************************
*
*ngbPhyInit() - PHY/SFP specific init
*/
LOCAL int ngbPhyInit
    (
     VXB_DEVICE_ID pDev
    )
    {
	NGB_DRV_CTRL * pDrvCtrl;
	pDrvCtrl = pDev->pDrvCtrl;

    int i;
    UINT16 value = 0;
    UINT32 lan_id;

    lan_id = (CSR_READ_4(pDev, NGB_CFG_PORT_ST) & 0x00000300U) >>8;

    for (i = 0;i < 15;i++)
        {
        if (!(CSR_READ_4(pDev, NGB_MIS_ST) & NGB_MIS_ST_GPHY_IN_RST(lan_id)))
            {
            break;
            }
        vxbMsDelay(1);
        }

    if (i == 15)
        {
        logMsg("GPhy reset exceeds maximum times.\n", 0, 0,0,0,0,0);
        return ERROR;
        }

    for (i = 0; i < 1000;i++)
        {
        ngbPhyRead(pDev, 29, 0xa43, &value);
        if (value & 0x20)
            {
            break;
            }
        }

    ngbPhyWrite(pDev, 20, 0xa46, 1);

    for (i = 0; i < 1000;i++)
        {
        ngbPhyRead(pDev, 29, 0xa43, &value);
        if (value & 0x20)
            {
            break;
            }
        }

    if (i == 1000)
        {
        logMsg("efuse config exceeds maximum 1000 tries.\n", 0, 0,0,0,0,0);
        return 0;
        }

    ngbPhyWrite(pDev, 20, 0xa46, 2);

    for (i = 0; i < 1000;i++)
        {
        ngbPhyRead(pDev, 29, 0xa43, &value);
        if (value & 0x20)
            {
            break;
            }
        }

    if (i == 1000)
        {
        logMsg("efuse enable exceeds maximum 1000 tries.\n", 0, 0,0,0,0,0);
        return 0;
        }

    for (i = 0; i < 1000;i++)
        {
        ngbPhyRead(pDev, 16, 0xa42, &value);
        if ((value & 0x7) == 3)
            {
            break;
            }
        }

    if (i == 1000)
        {
        logMsg("wait Lan on exceeds maximum 1000 tries.\n", 0, 0,0,0,0,0);
        return ERROR;
        }

    return 0;
    }

/************************************************************************
*
*ngbSetupLinkInternal() - phy setup link method
*/
LOCAL int ngbSetupLinkInternal
    (
    VXB_DEVICE_ID pDev,
    UINT32 speed
    )
    {
	NGB_DRV_CTRL * pDrvCtrl;
    pDrvCtrl = pDev->pDrvCtrl;

    UINT16 value = 0;

    if(!(speed & (NGB_LINK_SPEED_1GB_FULL | NGB_LINK_SPEED_10_FULL | NGB_LINK_SPEED_100_FULL)))
        return ERROR;

    /* disable 10/100M Half Duplex */
    ngbPhyRead(pDev, 4, 0, &value);

    value &= 0xFF5F;
    ngbPhyWrite(pDev, 4, 0, value);

    /* set advertise enable according to input speed */
    if (!(speed & NGB_LINK_SPEED_1GB_FULL))
        {
        ngbPhyRead(pDev, 9, 0, &value);
        value &= 0xFDFF;
        ngbPhyWrite(pDev, 9, 0, value);
        }
    else
        {
        ngbPhyRead(pDev, 9, 0, &value);
        value |= 0x200;
        ngbPhyWrite(pDev, 9, 0, value);
        }

    if (!(speed & NGB_LINK_SPEED_100_FULL))
        {
        ngbPhyRead(pDev, 4, 0, &value);
        value &= 0xFEFF;
        ngbPhyWrite(pDev, 4, 0, value);
        }
    else
        {
        ngbPhyRead(pDev, 4, 0, &value);
        value |= 0x100;
        ngbPhyWrite(pDev, 4, 0, value);
        }

    if (!(speed & NGB_LINK_SPEED_10_FULL))
        {
        ngbPhyRead(pDev, 4, 0, &value);
        value &= 0xFFBF;
        ngbPhyWrite(pDev, 4, 0, value);
        }
    else
        {
        ngbPhyRead(pDev, 4, 0, &value);
        value |= 0x40;
        ngbPhyWrite(pDev, 4, 0, value);
        }

    /* restart AN and wait AN done interrupt */
    value = NGB_MDI_PHY_RESTART_AN | NGB_MDI_PHY_ANE;
    ngbPhyWrite(pDev, 0, 0, value);

    value = 0x205B;
    ngbPhyWrite(pDev, 16, 0xd04, value);
    ngbPhyWrite(pDev, 17, 0xd04, 0);

    ngbPhyRead(pDev, 18, 0xd04, &value);
    value = value & 0xFFFC;
    value |= 0x2;
    ngbPhyWrite(pDev, 18, 0xd04, value);

    return 0;
    }

/************************************************************************
*
*ngbLinkIsUp() - phy check link is up or not method
*/
LOCAL BOOL ngbLinkIsUp
    (
    VXB_DEVICE_ID pDev
    )
    {
    NGB_DRV_CTRL * pDrvCtrl;
    pDrvCtrl = pDev->pDrvCtrl;

    UINT16 value = 0;
    UINT32 lan_speed = 2;
    UINT32 speed = 0;
    BOOL link_status = FALSE;
    UINT32 val;

    ngbPhyRead(pDev, 0x1A, 0xA43, &value);

    if ((value & 0x4) && (value != 0xFFFF))
        {
        link_status = TRUE;
        }
    else
        {
        return link_status;
        }

    /* refresh speed */
    if (link_status)
        {
        if ((value & 0x38) == 0x28)
            {
            speed = NGB_LINK_SPEED_1GB_FULL;
            }
        else if ((value & 0x38) == 0x18)
            {
            speed = NGB_LINK_SPEED_100_FULL;
            }
        else if ((value & 0x38) == 0x8)
            {
            speed = NGB_LINK_SPEED_10_FULL;
            }
        }
    else
        {
        speed = NGB_LINK_SPEED_UNKNOWN;
        }

    if (link_status && (speed != 0))
        {
        switch (speed)
            {
            case NGB_LINK_SPEED_100_FULL:
                lan_speed = 1;/*2‘b01*/
                break;
            case NGB_LINK_SPEED_1GB_FULL:
                lan_speed = 2;/*2‘b10*/
                break;
            case NGB_LINK_SPEED_10_FULL:
                lan_speed = 0;/*2‘b00*/
                break;
            default:
                break;
            }
        }

    val = CSR_READ_4(pDev, NGB_CFG_LAN_SPEED);
    val = ((val & ~0x3) | (lan_speed & 0x3));
    CSR_WRITE_4(pDev, NGB_CFG_LAN_SPEED, val);

    return link_status;
    }

/*****************************************************************************
*
* ngbeIvarWxSet - program interrupt vector allocation registers
*
* This routine programs the wangxun txgbe's interrupt vector allocation registers
* in order to allow an event on a given queue to trigger a particular
* interrupt source. For MSI-X interrupts, this controls which MSI-X vector
* is tied to a given queue. Note that there are fewer interrupt source
* bits and vectors than there are queues, so some of them will necessarily
* overlap.
*
* RETURNS: N/A
*
* ERRNO: N/A
*/

LOCAL void ngbIvarWxSet
    (
    VXB_DEVICE_ID pDev
    )
    {

    /* Allocate interrupt vectors */

    CSR_WRITE_4(pDev, NGB_PX_IVAR0, (NGB_PX_IVAR_RX0_DEFAULT |
                                     NGB_PX_IVAR_RX0_VALID |
                                     NGB_PX_IVAR_TX0_DEFAULT |
                                     NGB_PX_IVAR_TX0_VALID));

    CSR_WRITE_4(pDev, NGB_PX_MISC_IVAR, (NGB_PX_MISC_IVAR_DEFAULT |
                                         NGB_PX_MISC_IVAR_VALID));

    CSR_WRITE_4(pDev, NGB_PX_ITR0, (NGB_DEFAULT_ITR |
                                    NGB_PX_ITR_CNT_WDIS));

    return;
    }

/*****************************************************************************
*
* ngbEndLoad - END driver entry point
*
* This routine initializes the END interface instance associated
* with this device. In traditional END drivers, this function is
* the only public interface, and it's typically invoked by a BSP
* driver configuration stub. With VxBus, the BSP stub code is no
* longer needed, and this function is now invoked automatically
* whenever this driver's muxConnect() method is called.
*
* For older END drivers, the load string would contain various
* configuration parameters, but with VxBus this use is deprecated.
* The load string should just be an empty string. The second
* argument should be a pointer to the VxBus device instance
* associated with this device. Like older END drivers, this routine
* will still return the device name if the init string is empty,
* since this behavior is still expected by the MUX. The MUX will
* invoke this function twice: once to obtain the device name,
* and then again to create the actual END_OBJ instance.
*
* When this function is called the second time, it will initialize
* the END object, perform MIB2 setup, allocate a buffer pool, and
* initialize the supported END capabilities.
*
* RETURNS: An END object pointer, or NULL on error, or 0 and the name
* of the device if the <loadStr> was empty.
*
* ERRNO: N/A
*/

LOCAL END_OBJ * ngbEndLoad
    (
    char * loadStr,
    void * pArg
    )
    {
    NGB_DRV_CTRL *pDrvCtrl;
    VXB_DEVICE_ID pDev;
    int r;

    /* Make the MUX happy. */
    if (loadStr == NULL)
        {
        return NULL;
        }

    if (loadStr[0] == 0)
        {
        bcopy (NGB_NAME, loadStr, sizeof(NGB_NAME));
        return NULL;
        }

    pDev = pArg;
    pDrvCtrl = pDev->pDrvCtrl;

    if (END_OBJ_INIT (&pDrvCtrl->ngbEndObj, NULL, pDev->pName,
        pDev->unitNumber, &ngbNetFuncs,
        "WangXun Sapphire 1GbE VxBus END Driver") == ERROR)
        {
        logMsg("%s%d: END_OBJ_INIT failed\n", (int)NGB_NAME, pDev->unitNumber, 0, 0, 0, 0);
        return (NULL);
        }

    /*
     * Note: we lie about the interface speed here. While the
     * WangXun Emerald is really a 1GbE device, VxWorks currently stores
     * the interface speed in a 32-bit variable, and 10000000000
     * is too big to fit in it.
     */

    endM2Init (&pDrvCtrl->ngbEndObj, M2_ifType_ethernet_csmacd,
        pDrvCtrl->ngbAddr, ETHER_ADDR_LEN, ETHERMTU, 1000000000,
        IFF_NOTRAILERS | IFF_SIMPLEX | IFF_MULTICAST | IFF_BROADCAST);

    /*
     * The ifHighSpeed field we can set correctly. And since the
     * speed is always 10gig, we never need to change it.
     */

    if (pDrvCtrl->ngbEndObj.pMib2Tbl != NULL)
        {
        pDrvCtrl->ngbEndObj.pMib2Tbl->m2Data.mibXIfTbl.ifHighSpeed = 1000;
        pDrvCtrl->ngbEndObj.pMib2Tbl->m2Data.mibIfTbl.ifSpeed =
            pDrvCtrl->ngbEndObj.mib2Tbl.ifSpeed;
        }

    /* Allocate a buffer pool */
    if (pDrvCtrl->ngbMaxMtu == NGB_JUMBO_MTU)
        {
        r = endPoolJumboCreate ((2 * NGB_RX_DESC_CNT) + NGB_TX_DESC_CNT,
            &pDrvCtrl->ngbEndObj.pNetPool);
        }
    else
        {
        r = endPoolCreate ((2 * NGB_RX_DESC_CNT) + NGB_TX_DESC_CNT,
            &pDrvCtrl->ngbEndObj.pNetPool);
        }

    if (r == ERROR)
        {
        logMsg("%s%d: pool creation failed\n", (int)NGB_NAME, pDev->unitNumber, 0, 0, 0, 0);
        return (NULL);
        }

    pDrvCtrl->ngbPollBuf = endPoolTupleGet (pDrvCtrl->ngbEndObj.pNetPool);

    /* Set up polling stats. */

    pDrvCtrl->ngbEndStatsConf.ifPollInterval = sysClkRateGet();
    pDrvCtrl->ngbEndStatsConf.ifEndObj = &pDrvCtrl->ngbEndObj;
    pDrvCtrl->ngbEndStatsConf.ifWatchdog = NULL;
    pDrvCtrl->ngbEndStatsConf.ifValidCounters = (END_IFINUCASTPKTS_VALID |
        END_IFINMULTICASTPKTS_VALID | END_IFINBROADCASTPKTS_VALID |
        END_IFINOCTETS_VALID | END_IFINERRORS_VALID | END_IFINDISCARDS_VALID |
        END_IFOUTUCASTPKTS_VALID | END_IFOUTMULTICASTPKTS_VALID |
        END_IFOUTBROADCASTPKTS_VALID | END_IFOUTOCTETS_VALID |
        END_IFOUTERRORS_VALID);

    /* Set up capabilities. */

/*    pDrvCtrl->ngbCaps.cap_available = IFCAP_VLAN_MTU;*/
/*    pDrvCtrl->ngbCaps.cap_enabled = IFCAP_VLAN_MTU;*/

    pDrvCtrl->ngbCaps.csum_flags_tx = CSUM_IP|CSUM_TCP|CSUM_UDP;
    pDrvCtrl->ngbCaps.csum_flags_tx |= CSUM_TCPv6|CSUM_UDPv6;
    pDrvCtrl->ngbCaps.csum_flags_rx = CSUM_IP|CSUM_UDP|CSUM_TCP;
/*    pDrvCtrl->ngbCaps.cap_available |= IFCAP_TXCSUM|IFCAP_RXCSUM;*/
/*   pDrvCtrl->ngbCaps.cap_enabled |= IFCAP_TXCSUM|IFCAP_RXCSUM;*/


    if (pDrvCtrl->ngbMaxMtu == NGB_JUMBO_MTU)
        {
        pDrvCtrl->ngbCaps.cap_available |= IFCAP_JUMBO_MTU;
        pDrvCtrl->ngbCaps.cap_enabled |= IFCAP_JUMBO_MTU;
        }

    return (&pDrvCtrl->ngbEndObj);
    }

/*****************************************************************************
*
* ngbEndUnload - unload END driver instance
*
* This routine undoes the effects of ngbEndLoad(). The END object
* is destroyed, our network pool is released, the endM2 structures
* are released, and the polling stats watchdog is terminated.
*
* Note that the END interface instance can't be unloaded if the
* device is still running. The device must be stopped with muxDevStop()
* first.
*
* RETURNS: ERROR if device is still in the IFF_UP state, otherwise OK
*
* ERRNO: N/A
*/

LOCAL STATUS ngbEndUnload
    (
    END_OBJ * pEnd
    )
    {

    NGB_DRV_CTRL * pDrvCtrl;

    /* We must be stopped before we can be unloaded. */

    if (pEnd->flags & IFF_UP)
        {
        return (ERROR);
        }

    pDrvCtrl = (NGB_DRV_CTRL *)pEnd;

    netMblkClChainFree (pDrvCtrl->ngbPollBuf);

    /* Relase our buffer pool */
    endPoolDestroy (pDrvCtrl->ngbEndObj.pNetPool);

    /* terminate stats polling */
    wdDelete (pDrvCtrl->ngbEndStatsConf.ifWatchdog);

    endM2Free (&pDrvCtrl->ngbEndObj);

    END_OBJECT_UNLOAD (&pDrvCtrl->ngbEndObj);

    return (EALREADY);  /* prevent freeing of pDrvCtrl */

    }

/*****************************************************************************
*
* ngbEndHashTblPopulate - populate the multicast hash filter
*
* This function programs the wangxun txgbe's multicast hash
* filter to receive frames sent to the multicast groups specified
* in the multicast address list attached to the END object. If
* the interface is in IFF_ALLMULTI mode, the filter will be
* programmed to receive all multicast packets by setting all the
* bits in the hash table to one.
*
* RETURNS: N/A
*
* ERRNO: N/A
*/

LOCAL void ngbEndHashTblPopulate
    (
    NGB_DRV_CTRL * pDrvCtrl
    )
    {

    VXB_DEVICE_ID pDev;
    UINT32 h;
    UINT32 fctrl;
    int reg, bit;
    ETHER_MULTI * mCastNode = NULL;
    int i;
    UINT32 vmolr;

    pDev = pDrvCtrl->ngbDev;

    for (i = 0; i < 128; i++)
        CSR_WRITE_4(pDev, NGB_PSR_UC_TBL(0) + (i * sizeof(UINT32)), 0);

    for (i = 0; i < NGB_MAR_CNT; i++)
        CSR_WRITE_4(pDev, NGB_PSR_MC_TBL(0) + (i * sizeof(UINT32)), 0);

    vmolr = CSR_READ_4(pDev, NGB_PSR_VM_L2CTL(0)) &
                     ~(NGB_PSR_VM_L2CTL_UPE |
                       NGB_PSR_VM_L2CTL_MPE |
                       NGB_PSR_VM_L2CTL_ROPE |
                       NGB_PSR_VM_L2CTL_ROMPE);

    vmolr |= NGB_PSR_VM_L2CTL_BAM |
             NGB_PSR_VM_L2CTL_AUPE |
             NGB_PSR_VM_L2CTL_VACC;

    if (pDrvCtrl->ngbEndObj.flags & (IFF_ALLMULTI|IFF_PROMISC))
        {
        /* set allmulticast mode */
        fctrl = CSR_READ_4(pDev, NGB_PSR_CTL);
        fctrl |= (NGB_PSR_CTL_BAM |
                  NGB_PSR_CTL_UPE |
                  NGB_PSR_CTL_MPE);

        vmolr |= NGB_PSR_VM_L2CTL_MPE;
        CSR_WRITE_4(pDev, NGB_PSR_CTL, fctrl);

        return;
        }

    CSR_SETBIT_4(pDev, NGB_PSR_CTL, NGB_PSR_CTL_MFE);
    CSR_CLRBIT_4(pDev, NGB_PSR_CTL, NGB_PSR_CTL_MO);
    vmolr |= NGB_PSR_VM_L2CTL_ROPE | NGB_PSR_VM_L2CTL_ROMPE;

    /* Now repopulate it. */

    for (mCastNode = (ETHER_MULTI *) lstFirst (&pDrvCtrl->ngbEndObj.multiList);
         mCastNode != NULL;
         mCastNode = (ETHER_MULTI *) lstNext (&mCastNode->node))
        {
        h = (mCastNode->addr[4] >> 4) | (mCastNode->addr[5] << 4);
        h &= 0xFFF;
        reg = (h >> 5) & 0x7F;
        bit = h & 0x1F;
        CSR_SETBIT_4(pDev, NGB_PSR_MC_TBL(0) + (reg * sizeof(UINT32)), 1 << bit);
        }

    CSR_WRITE_4(pDev, NGB_PSR_VM_L2CTL(0), vmolr);

    return;
    }

/*****************************************************************************
*
* ngbEndMCastAddrAdd - add a multicast address for the device
*
* This routine adds a multicast address to whatever the driver
* is already listening for.  It then resets the address filter.
*
* RETURNS: OK or ERROR.
*
* ERRNO: N/A
*/

LOCAL STATUS ngbEndMCastAddrAdd
    (
    END_OBJ * pEnd,
    char * pAddr
    )
    {
    int retVal;
    NGB_DRV_CTRL * pDrvCtrl = (NGB_DRV_CTRL *) pEnd;

    semTake (pDrvCtrl->ngbDevSem, WAIT_FOREVER);

    if (!(pDrvCtrl->ngbEndObj.flags & IFF_UP))
        {
        semGive (pDrvCtrl->ngbDevSem);
        return (OK);
        }

    retVal = etherMultiAdd (&pEnd->multiList, pAddr);

    if (retVal == ENETRESET)
        {
        pEnd->nMulti++;
        ngbEndHashTblPopulate ((NGB_DRV_CTRL *)pEnd);
        }

    semGive (pDrvCtrl->ngbDevSem);
    return (OK);

    }

/*****************************************************************************
*
* ngbEndMCastAddrDel - delete a multicast address for the device
*
* This routine removes a multicast address from whatever the driver
* is listening for.  It then resets the address filter.
*
* RETURNS: OK or ERROR.
*
* ERRNO: N/A
*/

LOCAL STATUS ngbEndMCastAddrDel
    (
    END_OBJ * pEnd,
    char * pAddr
    )
    {
    int retVal;
    NGB_DRV_CTRL * pDrvCtrl = (NGB_DRV_CTRL *) pEnd;

    semTake (pDrvCtrl->ngbDevSem, WAIT_FOREVER);

    if (!(pDrvCtrl->ngbEndObj.flags & IFF_UP))
        {
        semGive (pDrvCtrl->ngbDevSem);
        return (OK);
        }

    retVal = etherMultiDel (&pEnd->multiList, pAddr);

    if (retVal == ENETRESET)
        {
        pEnd->nMulti--;
        ngbEndHashTblPopulate ((NGB_DRV_CTRL *)pEnd);
        }

    semGive (pDrvCtrl->ngbDevSem);
    return (OK);

    }

/*****************************************************************************
*
* ngbEndMCastAddrGet - get the multicast address list for the device
*
* This routine gets the multicast list of whatever the driver
* is already listening for.
*
* RETURNS: OK or ERROR.
*
* ERRNO: N/A
*/

LOCAL STATUS ngbEndMCastAddrGet
    (
    END_OBJ * pEnd,
    MULTI_TABLE * pTable
    )
    {
    STATUS rval;
    NGB_DRV_CTRL * pDrvCtrl = (NGB_DRV_CTRL *) pEnd;

    semTake (pDrvCtrl->ngbDevSem, WAIT_FOREVER);

    if (!(pDrvCtrl->ngbEndObj.flags & IFF_UP))
        {
        semGive (pDrvCtrl->ngbDevSem);
        return (OK);
        }

    rval = etherMultiGet (&pEnd->multiList, pTable);

    semGive (pDrvCtrl->ngbDevSem);
    return (rval);

    }

/*****************************************************************************
*
* ngbEndStatsDump - return polled statistics counts
*
* This routine is automatically invoked periodically by the polled
* statistics watchdog.
*
* RETURNS: always OK
*
* ERRNO: N/A
*/

LOCAL STATUS ngbEndStatsDump
    (
    NGB_DRV_CTRL * pDrvCtrl
    )
    {
    END_IFCOUNTERS * pEndStatsCounters;
    VXB_DEVICE_ID pDev;
    UINT32 tmp;
    int i = 0;

    semTake (pDrvCtrl->ngbDevSem, WAIT_FOREVER);

    if (!(pDrvCtrl->ngbEndObj.flags & IFF_UP))
        {
        semGive (pDrvCtrl->ngbDevSem);
        return (ERROR);
        }

    pDev = pDrvCtrl->ngbDev;

    pEndStatsCounters = &pDrvCtrl->ngbEndStatsCounters;

    /* Get number of RX'ed octets */

    tmp = CSR_READ_4(pDev, NGB_PX_GORC_LSB);
    pEndStatsCounters->ifInOctets = tmp;
    tmp = CSR_READ_4(pDev, NGB_PX_GORC_MSB); /* Read to clear counter */
    pEndStatsCounters->ifInOctets += (uint64_t)tmp << 32;

    /* Get number of TX'ed octets */

    tmp = CSR_READ_4(pDev, NGB_PX_GOTC_LSB);
    pEndStatsCounters->ifOutOctets = tmp;
    tmp = CSR_READ_4(pDev, NGB_PX_GOTC_MSB); /* Read to clear counter */
    pEndStatsCounters->ifOutOctets += (uint64_t)tmp << 32;

    /* Get RX'ed unicasts, broadcasts, multicasts */

    tmp = CSR_READ_4(pDev, NGB_PX_GPRC);
    pEndStatsCounters->ifInUcastPkts = tmp;
    tmp = CSR_READ_4(pDev, NGB_RX_BC_FRAMES_GOOD_LOW);
    pEndStatsCounters->ifInBroadcastPkts = tmp;
    tmp = 0;
    for(i = 0; i < 128; i++)
        tmp += CSR_READ_4(pDev, NGB_PX_MPRC(i));
    pEndStatsCounters->ifInMulticastPkts = tmp;
    pEndStatsCounters->ifInUcastPkts -= (pEndStatsCounters->ifInMulticastPkts +
        pEndStatsCounters->ifInBroadcastPkts);

    /* Get TX'ed unicasts, broadcasts, multicasts */

    tmp = CSR_READ_4(pDev, NGB_PX_GPTC);
    pEndStatsCounters->ifOutUcastPkts = tmp;
    tmp = CSR_READ_4(pDev, NGB_TX_BC_FRAMES_GOOD_LOW);
    pEndStatsCounters->ifOutBroadcastPkts = tmp;
    tmp = CSR_READ_4(pDev, NGB_TX_MC_FRAMES_GOOD_LOW);
    pEndStatsCounters->ifOutMulticastPkts = tmp;
    pEndStatsCounters->ifOutUcastPkts -=
        (pEndStatsCounters->ifOutMulticastPkts +
        pEndStatsCounters->ifOutBroadcastPkts);

    semGive (pDrvCtrl->ngbDevSem);

    return (OK);
    }

/*****************************************************************************
*
* ngbEndIoctl - the driver I/O control routine
*
* This function processes ioctl requests supplied via the muxIoctl()
* routine. In addition to the normal boilerplate END ioctls, this
* driver supports the IFMEDIA ioctls, END capabilities ioctls, and
* polled stats ioctls.
*
* RETURNS: A command specific response, usually OK or ERROR.
*
* ERRNO: N/A
*/

LOCAL int ngbEndIoctl
    (
    END_OBJ * pEnd,
    int cmd,
    caddr_t data
    )
    {
    NGB_DRV_CTRL * pDrvCtrl;
    END_MEDIALIST * mediaList;
    END_CAPABILITIES * hwCaps;
    END_MEDIA * pMedia;
    END_RCVJOBQ_INFO * qinfo;
    UINT32 nQs;
    VXB_DEVICE_ID pDev;
    INT32 value;
    int error = OK;

    pDrvCtrl = (NGB_DRV_CTRL *)pEnd;
    pDev = pDrvCtrl->ngbDev;

    if (cmd != EIOCPOLLSTART && cmd != EIOCPOLLSTOP)
        semTake (pDrvCtrl->ngbDevSem, WAIT_FOREVER);

    switch (cmd)
        {
        case EIOCSADDR:
            if (data == NULL)
                error = EINVAL;
            else
                {
                bcopy ((char *)data, (char *)pDrvCtrl->ngbAddr,
                    ETHER_ADDR_LEN);
                bcopy ((char *)data,
                    (char *)pEnd->mib2Tbl.ifPhysAddress.phyAddress,
                    ETHER_ADDR_LEN);
                if (pEnd->pMib2Tbl != NULL)
                    bcopy ((char *)data,
                        (char *)pEnd->pMib2Tbl->m2Data.mibIfTbl.ifPhysAddress.phyAddress,
                        ETHER_ADDR_LEN);
                }
            ngbEndRxConfig (pDrvCtrl);
            break;

        case EIOCGADDR:
            if (data == NULL)
                error = EINVAL;
            else
                bcopy ((char *)pDrvCtrl->ngbAddr, (char *)data,
                    ETHER_ADDR_LEN);
            break;

        case EIOCSFLAGS:
            value = (INT32) data;
            if (value < 0)
                {
                value = -value;
                value--;
                END_FLAGS_CLR (pEnd, value);
                }
            else
                END_FLAGS_SET (pEnd, value);
            ngbEndRxConfig (pDrvCtrl);
            break;

        case EIOCGFLAGS:
            if (data == NULL)
                error = EINVAL;
            else
                *(long *)data = END_FLAGS_GET(pEnd);
            break;

        case EIOCMULTIADD:
            error = ngbEndMCastAddrAdd (pEnd, (char *) data);
            break;

        case EIOCMULTIDEL:
            error = ngbEndMCastAddrDel (pEnd, (char *) data);
            break;

        case EIOCMULTIGET:
            error = ngbEndMCastAddrGet (pEnd, (MULTI_TABLE *) data);
            break;

        case EIOCPOLLSTART:
            pDrvCtrl->ngbPolling = TRUE;
            pDrvCtrl->ngbIntMask = CSR_READ_4(pDev, NGB_PX_MISC_IC);
            /*SR_WRITE_4(pDev, NGB_PX_MISC_ICS, NGB_PX_MISC_IEN_MASK & 0x00000000);*/

            /*
             * We may have been asked to enter polled mode while
             * there are transmissions pending. This is a problem,
             * because the polled transmit routine expects that
             * the TX ring will be empty when it's called. In
             * order to guarantee this, we have to drain the TX
             * ring here. We could also just plain reset and
             * reinitialize the transmitter, but this is faster.
             */
            while (CSR_READ_4(pDev, NGB_PX_TR_WP(0)) != CSR_READ_4(pDev, NGB_PX_TR_RP(0)));

            while (pDrvCtrl->ngbTxFree < NGB_TX_DESC_CNT)
                {
#ifdef NGB_VXB_DMA_BUF
                VXB_DMA_MAP_ID pMap;
#endif
                M_BLK_ID pMblk;

                pMblk = pDrvCtrl->ngbTxMblk[pDrvCtrl->ngbTxCons];

                if (pMblk != NULL)
                    {
#ifdef NGB_VXB_DMA_BUF
                    pMap = pDrvCtrl->ngbTxMblkMap[pDrvCtrl->ngbTxCons];
                    vxbDmaBufMapUnload (pDrvCtrl->ngbMblkTag, pMap);
#endif
                    endPoolTupleFree (pMblk);
                    pDrvCtrl->ngbTxMblk[pDrvCtrl->ngbTxCons] = NULL;
                    }

                pDrvCtrl->ngbTxFree++;
                NGB_INC_DESC (pDrvCtrl->ngbTxCons, NGB_TX_DESC_CNT);
                }
            break;

        case EIOCPOLLSTOP:
            pDrvCtrl->ngbPolling = FALSE;
            CSR_WRITE_4(pDev, NGB_PX_MISC_ICS, pDrvCtrl->ngbIntMask);
            break;

        case EIOCGMIB2233:
        case EIOCGMIB2:
            error = endM2Ioctl (&pDrvCtrl->ngbEndObj, cmd, data);
            break;

        case EIOCGPOLLCONF:
            if (data == NULL)
                error = EINVAL;
            else
                *((END_IFDRVCONF **)data) = &pDrvCtrl->ngbEndStatsConf;
            break;

        case EIOCGPOLLSTATS:
            if (data == NULL)
                error = EINVAL;
            else
                {
                error = ngbEndStatsDump(pDrvCtrl);
                if (error == OK)
                    *((END_IFCOUNTERS **)data) = &pDrvCtrl->ngbEndStatsCounters;
                }
            break;

        case EIOCGMEDIALIST:
            if (data == NULL)
                {
                error = EINVAL;
                break;
                }
            if (pDrvCtrl->ngbMediaList->endMediaListLen == 0)
                {
                error = ENOTSUP;
                break;
                }

            mediaList = (END_MEDIALIST *)data;
            if (mediaList->endMediaListLen <
                pDrvCtrl->ngbMediaList->endMediaListLen)
                {
                mediaList->endMediaListLen =
                    pDrvCtrl->ngbMediaList->endMediaListLen;
                error = ENOSPC;
                break;
                }

            bcopy((char *)pDrvCtrl->ngbMediaList, (char *)mediaList,
                  sizeof(END_MEDIALIST) + (sizeof(UINT32) *
                  pDrvCtrl->ngbMediaList->endMediaListLen));
            break;

        case EIOCGIFMEDIA:
            if (data == NULL)
                error = EINVAL;
            else
                {
                pMedia = (END_MEDIA *)data;
                pMedia->endMediaActive =
                    pDrvCtrl->ngbMediaList->endMediaListDefault;
                pMedia->endMediaStatus = pDrvCtrl->ngbCurStatus;
                }
            break;

        case EIOCSIFMEDIA:
            if (data == NULL)
                error = EINVAL;
            else
                {
                pMedia = (END_MEDIA *)data;
                pDrvCtrl->ngbCurMedia = pMedia->endMediaActive;
                ngbLinkUpdate (pDrvCtrl->ngbDev);
                error = OK;
                }
            break;

        case EIOCGIFCAP:
            hwCaps = (END_CAPABILITIES *)data;
            if (hwCaps == NULL)
                {
                error = EINVAL;
                break;
                }
            hwCaps->csum_flags_tx = pDrvCtrl->ngbCaps.csum_flags_tx;
            hwCaps->csum_flags_rx = pDrvCtrl->ngbCaps.csum_flags_rx;
            hwCaps->cap_available = pDrvCtrl->ngbCaps.cap_available;
            hwCaps->cap_enabled = pDrvCtrl->ngbCaps.cap_enabled;
            break;

        case EIOCSIFCAP:
            hwCaps = (END_CAPABILITIES *)data;
            if (hwCaps == NULL)
                {
                error = EINVAL;
                break;
                } /* fall through? */
        case EIOCGIFMTU:
            if (data == NULL)
                error = EINVAL;
            else
                *(INT32 *)data = pEnd->mib2Tbl.ifMtu;
            break;

        case EIOCSIFMTU:
            value = (INT32)data;
            if (value <= 0 || value > pDrvCtrl->ngbMaxMtu)
                {
                error = EINVAL;
                break;
                }
            pEnd->mib2Tbl.ifMtu = value;
            if (pEnd->pMib2Tbl != NULL)
                pEnd->pMib2Tbl->m2Data.mibIfTbl.ifMtu = value;
            break;

        case EIOCGRCVJOBQ:
            if (data == NULL)
                {
                error = EINVAL;
                break;
                }

            qinfo = (END_RCVJOBQ_INFO *)data;
            nQs = qinfo->numRcvJobQs;
            qinfo->numRcvJobQs = 1;
            if (nQs < 1)
                error = ENOSPC;
            else
                qinfo->qIds[0] = pDrvCtrl->ngbJobQueue;
            break;

        default:
            error = EINVAL;
            break;
        }

    if (cmd != EIOCPOLLSTART && cmd != EIOCPOLLSTOP)
        semGive (pDrvCtrl->ngbDevSem);

    return (error);
    }

/*****************************************************************************
*
* ngbEndRxConfig - configure the wangxun sapphire RX filter
*
* This is a helper routine used by ngbEndIoctl() and ngbEndStart() to
* configure the controller's RX filter. The unicast address filter is
* programmed with the currently configured MAC address, and the RX
* configuration is set to allow unicast and broadcast frames to be
* received. If the interface is in IFF_PROMISC mode, the FCTL_UPE
* bit is set, which allows all packets to be received.
*
* The ngbEndHashTblPopulate() routine is also called to update the
* multicast filter.
*
* RETURNS: N/A
*
* ERRNO: N/A
*/

LOCAL void ngbEndRxConfig
    (
    NGB_DRV_CTRL * pDrvCtrl
    )
    {
    UINT32 addr[2] = {0,0};
    VXB_DEVICE_ID pDev;
    UINT32 parLo;
    UINT32 parHi;
    int i;

    /* set rar */
    if (pDrvCtrl->ngbDevType == NGB_DEVTYPE_COPPER)
        {
        parLo = NGB_PSR_MAC_SWC_AD_L;
        parHi = NGB_PSR_MAC_SWC_AD_H;
        }

    pDev = pDrvCtrl->ngbDev;

    /* Copy the address to a buffer we know is 32-bit aligned. */

    /* bcopy ((char *)pDrvCtrl->ngbAddr, (char *)addr, ETHER_ADDR_LEN); */
    for(i = 0; i < 2; i++)
        {
        addr[1] |= (UINT32)(((UINT32)(pDrvCtrl->ngbAddr[i])) << ((1 - i) * 8));
        }

    for(i = 0; i < 4; i++)
        {
        addr[0] |= (UINT32)(((UINT32)(pDrvCtrl->ngbAddr[i + 2])) << ((3 - i) * 8));
        }

    /* Init our MAC address.*/
    CSR_WRITE_4(pDev, NGB_PSR_MAC_SWC_IDX, 0);

    CSR_WRITE_4(pDev, parLo, htole32(addr[0]));
    CSR_WRITE_4(pDev, parHi, (htole32(addr[1]) & 0xFFFF) | NGB_PARH_AV);

    /* Program the RX filter to receive unicasts and broadcasts */

    CSR_SETBIT_4(pDev, NGB_PSR_CTL, NGB_PSR_CTL_BAM);

    /* Enable promisc mode, if specified. */

    if (pDrvCtrl->ngbEndObj.flags & IFF_PROMISC)
        {
        CSR_SETBIT_4(pDev, NGB_PSR_CTL, NGB_PSR_CTL_UPE);
        }
    else
        {
        CSR_CLRBIT_4(pDev, NGB_PSR_CTL, NGB_PSR_CTL_UPE);
        }

    /* Program the multicast filter. */
    ngbEndHashTblPopulate (pDrvCtrl);

    return;
    }

/*****************************************************************************
*
* ngbEndStart - start the device
*
* This function resets the device to put it into a known state and
* then configures it for RX and TX operation. The RX and TX configuration
* registers are initialized, and the address of the RX DMA window is
* loaded into the device. Interrupts are then enabled, and the initial
* link state is configured.
*
* Note that this routine also checks to see if an alternate jobQueue
* has been specified via the vxbParam subsystem. This allows the driver
* to divert its work to an alternate processing task, such as may be
* done with TIPC. This means that the jobQueue can be changed while
* the system is running, but the device must be stopped and restarted
* for the change to take effect.
*
* RETURNS: ERROR if device initialization failed, otherwise OK
*
* ERRNO: N/A
*/

LOCAL STATUS ngbEndStart
    (
    END_OBJ * pEnd
    )
    {
    NGB_DRV_CTRL * pDrvCtrl;
    VXB_DEVICE_ID pDev;
    VXB_INST_PARAM_VALUE val;
    HEND_RX_QUEUE_PARAM * pRxQueue;
    NGB_ADV_RDESC * pDesc;
    M_BLK_ID pMblk;
#ifdef NGB_VXB_DMA_BUF
    VXB_DMA_MAP_ID pMap;
#else
    UINT32 addr;
#endif
    int i;
    UINT32 dctrl = 0;
    UINT32 rx_dis;
    UINT32 tx_dis;
    UINT32 lan_id = 0;

    pDrvCtrl = (NGB_DRV_CTRL *)pEnd;
    pDev = pDrvCtrl->ngbDev;

    semTake (pDrvCtrl->ngbDevSem, WAIT_FOREVER);
    END_TX_SEM_TAKE (pEnd, WAIT_FOREVER);

    if (pEnd->flags & IFF_UP)
        {
        END_TX_SEM_GIVE (pEnd);
        semGive (pDrvCtrl->ngbDevSem);
        return (OK);
        }

    /* Get lan id*/
    lan_id = (CSR_READ_4(pDev, NGB_CFG_PORT_ST) & 0x00000300U) >> 8;

    /* Initialize job queues */

    pDrvCtrl->ngbJobQueue = netJobQueueId;

    /* Override the job queue ID if the user supplied an alternate one. */

    if (vxbInstParamByNameGet (pDev, "rxQueue00",
        VXB_PARAM_POINTER, &val) == OK)
        {
        pRxQueue = (HEND_RX_QUEUE_PARAM *) val.pValue;
        if (pRxQueue->jobQueId != NULL)
            {
            pDrvCtrl->ngbJobQueue = pRxQueue->jobQueId;
            }
        }


    QJOB_SET_PRI(&pDrvCtrl->ngbIntJob, NET_TASK_QJOB_PRI);
    pDrvCtrl->ngbIntJob.func = ngbEndIntHandle;

    vxAtomicSet (&pDrvCtrl->ngbIntPending, FALSE);

    ngbReset (pDev);

    /* Set up the RX ring. */

    bzero ((char *)pDrvCtrl->ngbRxDescMem,
            sizeof(NGB_ADV_RDESC) * NGB_RX_DESC_CNT);

    for (i = 0; i < NGB_RX_DESC_CNT; i++)
        {
        pMblk = endPoolTupleGet (pDrvCtrl->ngbEndObj.pNetPool);
        if (pMblk == NULL)
            {
            /* TODO: cleanup */
            END_TX_SEM_GIVE (pEnd);
            semGive (pDrvCtrl->ngbDevSem);
            return (ERROR);
            }

        pMblk->m_next = NULL;
        NGB_ADJ (pMblk);
        pDrvCtrl->ngbRxMblk[i] = pMblk;

        pDesc = &pDrvCtrl->ngbRxDescMem[i];

#ifdef NGB_VXB_DMA_BUF

        pMap = pDrvCtrl->ngbRxMblkMap[i];

        vxbDmaBufMapMblkLoad (pDev, pDrvCtrl->ngbMblkTag, pMap, pMblk, 0);

        /*
         * Currently, pointers in VxWorks are always 32 bits, even on
         * 64-bit architectures. This means that for the time being,
         * we can save a few cycles by only setting the addrlo field
         * in DMA descriptors and leaving the addrhi field set to 0.
         * When pointers become 64 bits wide, this will need to be
         * changed.
         */

#ifdef NGB_64
        pDesc->read.ngb_addrlo =
        htole32(NGB_ADDR_LO(pMap->fragList[0].frag));
        pDesc->read.ngb_addrhi =
        htole32(NGB_ADDR_HI(pMap->fragList[0].frag));
#else
        pDesc->read.ngb_addrlo = htole32((UINT32)pMap->fragList[0].frag);
        pDesc->read.ngb_addrhi = 0;
#endif
#else  /* NGB_VXB_DMA_BUF */
        addr = (UINT32)pMblk->m_data;
        if (cacheUserFuncs.virtToPhysRtn)
            addr = cacheUserFuncs.virtToPhysRtn (addr);
        pDesc->read.ngb_addrlo = htole32(addr);
        pDesc->read.ngb_addrhi = 0;
#endif /* NGB_VXB_DMA_BUF */

        pDesc->read.ngb_hdrlo = 0;
        pDesc->read.ngb_hdrhi = 0;
        }

    /* Set up the TX ring. */

    bzero ((char *)pDrvCtrl->ngbTxDescMem,
            sizeof(NGB_ADV_TDESC) * NGB_TX_DESC_CNT);

    bzero ((char *)pDrvCtrl->isb_mem,
            sizeof(UINT32) * NGB_ISB_MAX);

#ifdef NGB_VXB_DMA_BUF
    /* Load the maps for the RX and TX DMA ring. */

    vxbDmaBufMapLoad (pDev, pDrvCtrl->ngbRxDescTag,
                pDrvCtrl->ngbRxDescMap, pDrvCtrl->ngbRxDescMem,
                sizeof(NGB_ADV_RDESC) * NGB_RX_DESC_CNT, 0);

    vxbDmaBufMapLoad (pDev, pDrvCtrl->ngbTxDescTag,
                pDrvCtrl->ngbTxDescMap, pDrvCtrl->ngbTxDescMem,
                sizeof(NGB_ADV_TDESC) * NGB_TX_DESC_CNT, 0);

    vxbDmaBufMapLoad (pDev, pDrvCtrl->ngbIsbTag,
                pDrvCtrl->ngbIsbMap, pDrvCtrl->isb_mem,
                sizeof(UINT32) * NGB_ISB_MAX, 0);
#endif

    /* Initialize state */
    pDrvCtrl->ngbRxIdx = 0;
    pDrvCtrl->ngbMoreRx = 0;
    pDrvCtrl->ngbTxStall = FALSE;
    pDrvCtrl->ngbTxProd = 0;
    pDrvCtrl->ngbTxCons = 0;
    pDrvCtrl->ngbTxFree = NGB_TX_DESC_CNT;
    pDrvCtrl->ngbLastCtx = 0xFFFF;
#ifdef CSUM_IPHDR_OFFSET
    pDrvCtrl->ngbLastOffsets = -1;
#else
    pDrvCtrl->ngbLastIpLen = -1;
#endif
    pDrvCtrl->ngbLastVlan = 0;

    /* Program the RX filter. */

    ngbEndRxConfig (pDrvCtrl);

    CSR_WRITE_4(pDev, NGB_RDB_PB_SZ(0), 42 << 10);
    CSR_WRITE_4(pDev, NGB_TDM_PB_THRE, 0xa);

    /* Initialize RX and TX queue zero */
    CSR_CLRBIT_4(pDev, NGB_PX_RR_CFG(0), NGB_PX_RR_CFG_RR_EN);
    CSR_CLRBIT_4(pDev, NGB_PX_TR_CFG(0), NGB_PX_TR_CFG_ENABLE);

    /* configure tx ring */
#ifdef NGB_VXB_DMA_BUF
    CSR_WRITE_4(pDev, NGB_PX_TR_BAH(0),
                      NGB_ADDR_HI(pDrvCtrl->ngbTxDescMap->fragList[0].frag));
    CSR_WRITE_4(pDev, NGB_PX_TR_BAL(0),
                      NGB_ADDR_LO(pDrvCtrl->ngbTxDescMap->fragList[0].frag));
    CSR_WRITE_4(pDev, NGB_PX_TR_BAH(0), 0);
#else
    CSR_WRITE_4(pDev, NGB_PX_TR_BAH(0), 0);
    addr = (UINT32)pDrvCtrl->ngbTxDescMem;
    if (cacheUserFuncs.virtToPhysRtn)
        addr = cacheUserFuncs.virtToPhysRtn (addr);
    CSR_WRITE_4(pDev, NGB_PX_TR_BAL(0), addr);
#endif

    CSR_WRITE_4(pDev, NGB_PX_TR_WP(0), 0);
    CSR_WRITE_4(pDev, NGB_PX_TR_RP(0), 0);

    dctrl = CSR_READ_4(pDev, NGB_PX_TR_CFG(0));
    dctrl |= (NGB_TX_DESC_CNT / 128) << NGB_PX_TR_CFG_TR_SIZE_SHIFT |
             0x20 << NGB_PX_TR_CFG_WTHRESH_SHIFT;
    CSR_WRITE_4(pDev, NGB_PX_TR_CFG(0), dctrl);
    /* configure rx ring */
#ifdef NGB_VXB_DMA_BUF
    CSR_WRITE_4(pDev, NGB_PX_RR_BAH(0),
                      NGB_ADDR_HI(pDrvCtrl->ngbRxDescMap->fragList[0].frag));
    CSR_WRITE_4(pDev, NGB_PX_RR_BAL(0),
                      NGB_ADDR_LO(pDrvCtrl->ngbRxDescMap->fragList[0].frag));
#else
    CSR_WRITE_4(pDev, NGB_PX_RR_BAH(0), 0);
    addr = (UINT32)pDrvCtrl->ngbRxDescMem;
    if (cacheUserFuncs.virtToPhysRtn)
        addr = cacheUserFuncs.virtToPhysRtn (addr);
    CSR_WRITE_4(pDev, NGB_PX_RR_BAL(0), addr);
#endif

    CSR_WRITE_4(pDev, NGB_PX_RR_WP(0), NGB_RX_DESC_CNT - 1);
    CSR_WRITE_4(pDev, NGB_PX_RR_RP(0), 0);

    dctrl = CSR_READ_4(pDev, NGB_PX_RR_CFG(0));
    dctrl |= (NGB_RX_DESC_CNT / 128) << NGB_PX_RR_CFG_RR_SIZE_SHIFT |
              0x1 << NGB_PX_RR_CFG_RR_THER_SHIFT;
    dctrl &= ~(NGB_PX_RR_CFG_RR_HDR_SZ |
               NGB_PX_RR_CFG_RR_BUF_SZ |
               NGB_PX_RR_CFG_SPLIT_MODE);
    dctrl |= NGB_PX_RR_CFG_HDR_SIZE << NGB_PX_RR_CFG_BSIZEHDRSIZE_SHIFT;

    if (pDrvCtrl->ngbMaxMtu == NGB_JUMBO_MTU)
        dctrl |= NGB_PX_RR_CFG_BSIZE_10K >> NGB_PX_RR_CFG_BSIZEPKT_SHIFT;
    else
        dctrl |= NGB_PX_RR_CFG_BSIZE_DEFAULT >> NGB_PX_RR_CFG_BSIZEPKT_SHIFT;
    CSR_WRITE_4(pDev, NGB_PX_RR_CFG(0), dctrl);
    CSR_SETBIT_4(pDev, NGB_PX_RR_CFG(0), NGB_PX_RR_CFG_DROP_EN);

    /* Select advanced RX descriptors and enable VLAN stripping */
    if (pDrvCtrl->ngbCaps.cap_enabled & IFCAP_VLAN_HWTAGGING)
        CSR_SETBIT_4(pDev, NGB_PX_RR_CFG(0), NGB_PX_RR_CFG_VLAN);
    else
        CSR_CLRBIT_4(pDev, NGB_PX_RR_CFG(0), NGB_PX_RR_CFG_VLAN);

    /* Set frame sizes */

    if (pDrvCtrl->ngbMaxMtu == NGB_JUMBO_MTU)
        {
        CSR_WRITE_4(pDev, NGB_PSR_MAX_SZ, 9018 & 0x0000FFFF);
        CSR_SETBIT_4(pDev, NGB_MAC_RX_CFG, NGB_MAC_RX_CFG_JE);
        }
    else
        {
        CSR_WRITE_4(pDev, NGB_PSR_MAX_SZ, 1518 & 0x0000FFFF);
        CSR_CLRBIT_4(pDev, NGB_MAC_RX_CFG, NGB_MAC_RX_CFG_JE);
        }

    /*rsec and tsec*/
    rx_dis = CSR_READ_4(pDev, NGB_RSC_CTL);
    rx_dis = rx_dis & (~0x2);
    CSR_WRITE_4(pDev, NGB_RSC_CTL, rx_dis);

    tx_dis = CSR_READ_4(pDev, NGB_TSEC_CTL);
    tx_dis = tx_dis & (~0x2);
    CSR_WRITE_4(pDev, NGB_TSEC_CTL, tx_dis);

    CSR_SETBIT_4(pDev, NGB_TSEC_BUF_AE, 0x3FF & 0x10);
    CSR_WRITE_4(pDev, NGB_PX_GPIE, 0);

    if (pDrvCtrl->ngbDevType == NGB_DEVTYPE_COPPER)
        {
        CSR_SETBIT_4(pDev, NGB_RSC_CTL, NGB_RSC_CTL_CRC_STRIP);
        }

    /* TODO: if autonegotiation is enabled, this bit should be set by software
       to the negotiated flow control value... */
/*   if (pDrvCtrl->ngbDevType == NGB_DEVTYPE_COPPER)*/
/*       CSR_SETBIT_4(pDev, NGB_MAC_RX_FLOW_CTRL, NGB_MAC_RX_FLOW_CTRL_RFE);*/

    /*
     * Enable interrupts
     * We assign RX queue 0 events to interrupt source 0 and TX
     * queue 0 events to interrupt source 1.
     */
#ifdef NGB_VXB_DMA_BUF
    CSR_WRITE_4(pDev, NGB_PX_ISB_ADDR_L,
                      NGB_ADDR_LO(pDrvCtrl->ngbIsbMap->fragList[0].frag));

#ifdef NGB_64
    CSR_WRITE_4(pDev, NGB_PX_ISB_ADDR_H,
                      NGB_ADDR_HI(pDrvCtrl->ngbIsbMap->fragList[0].frag));
#else
    CSR_WRITE_4(pDev, NGB_PX_ISB_ADDR_H, 0);
#endif /* NGB_64 */

#else /* NGB_VXB_DMA_BUF */
    addr = (UINT32)pDrvCtrl->isb_mem;
    if (cacheUserFuncs.virtToPhysRtn)
        addr = cacheUserFuncs.virtToPhysRtn (addr);
    CSR_WRITE_4(pDev, NGB_PX_ISB_ADDR_L, NGB_ADDR_LO(addr));
#ifdef NGB_64
    CSR_WRITE_4(pDev, NGB_PX_ISB_ADDR_H,
                      NGB_ADDR_HI(addr));
#else
    CSR_WRITE_4(pDev, NGB_PX_ISB_ADDR_H, 0);
#endif /* NGB_64 */

#endif /* NGB_VXB_DMA_BUF */

    vxbIntEnable (pDev, 0, ngbEndInt, pDrvCtrl);
    if (pDrvCtrl->ngbDevType == NGB_DEVTYPE_COPPER)
        {
        ngbIvarWxSet (pDev);
        }

    /* clear any pending interrupts, may auto mask */
    CSR_READ_4(pDev, NGB_PX_IC(0));
    CSR_READ_4(pDev, NGB_PX_MISC_IC);

    /* enable irq */ 
    CSR_WRITE_4(pDev, NGB_PX_MISC_IEN, NGB_PX_MISC_IEN_MASK);
    CSR_WRITE_4(pDev, NGB_PX_IMC(0), 0x1ff);

    /* Initialize transmitter. */
    CSR_SETBIT_4(pDev, NGB_MAC_RX_CFG, NGB_MAC_RX_CFG_RE);
    CSR_SETBIT_4(pDev, NGB_TDM_CTL, NGB_TDM_CTL_TE);
    CSR_SETBIT_4(pDev, NGB_MAC_TX_CFG, NGB_MAC_TX_CFG_TE | NGB_MAC_TX_CFG_SPEED_1G);

    CSR_SETBIT_4(pDev, NGB_PX_RR_CFG(0), NGB_PX_RR_CFG_RR_EN);
    CSR_SETBIT_4(pDev, NGB_PX_TR_CFG(0), NGB_PX_TR_CFG_ENABLE);

    /* Enable receiver and transmitter */
    CSR_SETBIT_4(pDev, NGB_RDB_PB_CTL, NGB_RDB_PB_CTL_RXEN);

    /*Phy init and config*/
    ngbPhySetupOnce(pDev);

    /* Check phy is up. No need here*/
/*   ngbSetupCheckLink(pDrvCtrl);*/

    /*Driver Load flag*/
    CSR_SETBIT_4(pDev, NGB_CFG_PORT_CTL, NGB_CFG_PORT_CTL_DRV_LOAD);
    CSR_SETBIT_4(pDev, NGB_CFG_PORT_CTL, NGB_CFG_PORT_CTL_PFRSTD);

    /* Set initial link state */
#if 0
    value = 0x205B;
    ngbPhyWrite(pDev, 16, 0xd04, value);
#endif

    pDrvCtrl->ngbCurMedia = IFM_ETHER|IFM_NONE;
    pDrvCtrl->ngbCurStatus = IFM_AVALID;
    END_FLAGS_SET (pEnd, (IFF_UP | IFF_RUNNING));

#if 0   /* Enable linkupdate to fix network issue*/
    if(lan_id == 0)
        {
        ngbLinkUpdate (pDev);
        }
#endif

    END_TX_SEM_GIVE (pEnd);
    semGive (pDrvCtrl->ngbDevSem);

    return (OK);

    }

/*****************************************************************************
*
* ngbEndStop - stop the device
*
* This function undoes the effects of ngbEndStart(). The device is shut
* down and all resources are released. Note that the shutdown process
* pauses to wait for all pending RX, TX and link event jobs that may have
* been initiated by the interrupt handler to complete. This is done
* to prevent tNetTask from accessing any data that might be released by
* this routine.
*
* RETURNS: ERROR if device shutdown failed, otherwise OK
*
* ERRNO: N/A
*/

LOCAL STATUS ngbEndStop
    (
    END_OBJ * pEnd
    )
    {
    NGB_DRV_CTRL * pDrvCtrl;
    VXB_DEVICE_ID pDev;
    int i;

    pDrvCtrl = (NGB_DRV_CTRL *)pEnd;
    pDev = pDrvCtrl->ngbDev;

    semTake (pDrvCtrl->ngbDevSem, WAIT_FOREVER);

    if (!(pEnd->flags & IFF_UP))
        {
        semGive (pDrvCtrl->ngbDevSem);
        return (OK);
        }

    /* Disable interrupts */
    /*vxbIntDisable (pDev, 0, teiEndInt, pDrvCtrl);*/
    CSR_WRITE_4(pDev, NGB_PX_MISC_IEN, 0);
    /*
     * Wait for all jobs to drain.
     * Note: this must be done before we disable the receiver
     * and transmitter below. If someone tries to reboot us via
     * WDB, this routine may be invoked while the RX handler is
     * still running in tNetTask. If we disable the chip while
     * that function is running, it'll start reading inconsistent
     * status from the chip. We have to wait for that job to
     * terminate first, then we can disable the receiver and
     * transmitter.
     */

    for (i = 0; i < NGB_TIMEOUT; i++)
        {
        if (vxAtomicGet (&pDrvCtrl->ngbIntPending) == FALSE)
            break;
        taskDelay(1);
        }

    if (i == NGB_TIMEOUT)
        logMsg("%s%d: timed out waiting for job to complete\n",
            (int)NGB_NAME, pDev->unitNumber, 0, 0, 0, 0);

    END_TX_SEM_TAKE (pEnd, WAIT_FOREVER);

    END_FLAGS_CLR (pEnd, (IFF_UP | IFF_RUNNING));

    /* Disable RX and TX. */

    ngbReset (pDev);

    /* Release resources */

#ifdef NGB_VXB_DMA_BUF
    vxbDmaBufMapUnload (pDrvCtrl->ngbRxDescTag, pDrvCtrl->ngbRxDescMap);
    vxbDmaBufMapUnload (pDrvCtrl->ngbTxDescTag, pDrvCtrl->ngbTxDescMap);
    vxbDmaBufMapUnload (pDrvCtrl->ngbIsbTag, pDrvCtrl->ngbIsbMap);
#endif

    for (i = 0; i < NGB_RX_DESC_CNT; i++)
        {
        if (pDrvCtrl->ngbRxMblk[i] != NULL)
            {
            netMblkClChainFree (pDrvCtrl->ngbRxMblk[i]);
            pDrvCtrl->ngbRxMblk[i] = NULL;
#ifdef NGB_VXB_DMA_BUF
            vxbDmaBufMapUnload (pDrvCtrl->ngbMblkTag,
                pDrvCtrl->ngbRxMblkMap[i]);
#endif
            }
        }
    endMcacheFlush ();

    for (i = 0; i < NGB_TX_DESC_CNT; i++)
        {
        if (pDrvCtrl->ngbTxMblk[i] != NULL)
            {
            netMblkClChainFree (pDrvCtrl->ngbTxMblk[i]);
            pDrvCtrl->ngbTxMblk[i] = NULL;
#ifdef NGB_VXB_DMA_BUF
            vxbDmaBufMapUnload (pDrvCtrl->ngbMblkTag,
                pDrvCtrl->ngbTxMblkMap[i]);
#endif
            }
        }

    END_TX_SEM_GIVE (pEnd);
    semGive (pDrvCtrl->ngbDevSem);
    return (OK);

    }

/***************************************************************************
*
* This routine is scheduled to run in tNetTask by the interrupt service
* routine whenever a chip interrupt occurs. This function will check
* what interrupt events are pending and schedule additional jobs to
* service them. Once there are no more events waiting to be serviced
* (i.e. the chip has gone idle), interrupts will be unmasked so that
* the ISR can fire again.
*
* RETURNS: N/A
*
* ERRNO: N/A
*/

LOCAL void ngbEndIntHandle
    (
    void * pArg
    )
    {
    QJOB *pJob;
    NGB_DRV_CTRL *pDrvCtrl;
    VXB_DEVICE_ID pDev;
    UINT32 icr;
    UINT16 value = 0;
    int loopCtrl = 1;
    UINT32 icr_misc;

    pJob = pArg;
    pDrvCtrl = member_to_object (pJob, NGB_DRV_CTRL, ngbIntJob);
    pDev = pDrvCtrl->ngbDev;

    icr = CSR_READ_4(pDev, NGB_PX_IMS(0));
    icr_misc = pDrvCtrl->isb_mem[NGB_ISB_MISC];
    icr_misc = htole32(icr_misc);
    pDrvCtrl->isb_mem[NGB_ISB_MISC] = 0;
    icr_misc = icr_misc & (NGB_PX_MISC_IEN_ETH_LKDN | NGB_PX_MISC_IEN_IC_PHY);

    if ((icr & 0x2) | icr_misc)
        {
        ngbPhyRead(pDev, 0x1d, 0xa43, &value);
        if (value & 0x10) 
            {
            ngbLinkUpdate (pDev);
            }
        }

    if (icr & 0x1)
        {
        ngbEndTxHandle (pDrvCtrl);
        loopCtrl = ngbEndRxHandle (pDrvCtrl);

        if (loopCtrl == 0)
            {
            jobQueuePost (pDrvCtrl->ngbJobQueue, &pDrvCtrl->ngbIntJob);
            return;
            }
        }

    vxAtomicSet (&pDrvCtrl->ngbIntPending, FALSE);

    /* Unmask interrupts here */
    CSR_WRITE_4(pDev, NGB_PX_MISC_IEN, NGB_PX_MISC_IEN_MASK);

    CSR_WRITE_4(pDev, NGB_PX_IMC(0), icr & 0x1FF);

    return;
    }

/*****************************************************************************
*
* ngbEndInt - handle device interrupts
*
* This function is invoked whenever the wangxun txgbe's interrupt line is asserted.
*
* RETURNS: N/A
*
* ERRNO: N/A
*/

LOCAL void ngbEndInt
    (
    NGB_DRV_CTRL * pDrvCtrl
    )
    {
    UINT32 isb;
    UINT32 isb_misc;

    VXB_DEVICE_ID pDev;

    pDev = pDrvCtrl->ngbDev;

    isb = CSR_READ_4(pDev, NGB_PX_IMS(0)) & 0x3;
    isb_misc = pDrvCtrl->isb_mem[NGB_ISB_MISC];
    isb_misc = htole32(isb_misc);
    isb_misc = isb_misc & (NGB_PX_MISC_IEN_ETH_LKDN | NGB_PX_MISC_IEN_IC_PHY);

    if (!(isb | isb_misc))
        return;
    CSR_WRITE_4(pDev, NGB_PX_INTA, 1);

    if (vxCas (&pDrvCtrl->ngbIntPending, FALSE, TRUE))
        {
        jobQueuePost (pDrvCtrl->ngbJobQueue, &pDrvCtrl->ngbIntJob);
        }

    return;
    }

/******************************************************************************
*
* ngbEndRxHandle - process received frames
*
* This function is scheduled by the ISR to run in the context of tNetTask
* whenever an RX interrupt is received. It processes packets from the
* RX window and encapsulates them into mBlk tuples which are handed up
* to the MUX.
*
* There may be several packets waiting in the window to be processed.
* We take care not to process too many packets in a single run through
* this function so as not to monopolize tNetTask and starve out other
* jobs waiting in the jobQueue. If we detect that there's still more
* packets waiting to be processed, we queue ourselves up for another
* round of processing.
*
* RETURNS: N/A
*
* ERRNO: N/A
*/

LOCAL int ngbEndRxHandle
    (
    NGB_DRV_CTRL *pDrvCtrl
    )
    {
    VXB_DEVICE_ID pDev;
    M_BLK_ID pMblk;
    M_BLK_ID pNewMblk;
    NGB_ADV_RDESC * pDesc;
#ifdef NGB_VXB_DMA_BUF
    VXB_DMA_MAP_ID pMap;
#endif
    int loopCounter = NGB_MAX_RX;
    pDev = pDrvCtrl->ngbDev;

    pDesc = (NGB_ADV_RDESC *)&pDrvCtrl->ngbRxDescMem[pDrvCtrl->ngbRxIdx];

    while (loopCounter)
        {
        if (!(pDesc->write.ngb_sts & htole16(NGB_ADV_RDESC_STS_DD)))
            {
            break;
            }

#ifdef NGB_VXB_DMA_BUF
        pMap = pDrvCtrl->ngbRxMblkMap[pDrvCtrl->ngbRxIdx];
#endif
        pMblk = pDrvCtrl->ngbRxMblk[pDrvCtrl->ngbRxIdx];
        /*
         * Ignore checksum errors here. They'll be handled by the
         * checksum offload code below, or by the stack.
         */

        pNewMblk = endPoolTupleGet (pDrvCtrl->ngbEndObj.pNetPool);
        if (pNewMblk == NULL)
            {
            logMsg("%s%d: out of mBlks at %d\n", (int)NGB_NAME,
                pDev->unitNumber, pDrvCtrl->ngbRxIdx,0,0,0);
            pDrvCtrl->ngbLastError.errCode = END_ERR_NO_BUF;
            muxError (&pDrvCtrl->ngbEndObj, &pDrvCtrl->ngbLastError);

#ifdef NGB_VXB_DMA_BUF
            pDesc->read.ngb_addrlo =
                htole32(NGB_ADDR_LO(pMap->fragList[0].frag));
            pDesc->read.ngb_addrhi =
                htole32(NGB_ADDR_HI(pMap->fragList[0].frag));
#else
            UINT32 addr = (UINT32)pMblk->m_data;
            if (cacheUserFuncs.virtToPhysRtn)
                addr = cacheUserFuncs.virtToPhysRtn (addr);
            pDesc->read.ngb_addrlo = htole32(addr);
            pDesc->read.ngb_addrhi = 0;
#endif
            pDesc->read.ngb_hdrlo = 0;
            pDesc->read.ngb_hdrhi = 0;

            CSR_WRITE_4(pDev, NGB_PX_RR_WP(0), pDrvCtrl->ngbRxIdx);
            NGB_INC_DESC(pDrvCtrl->ngbRxIdx, NGB_RX_DESC_CNT);
            loopCounter--;
            pDesc =
                (NGB_ADV_RDESC *)&pDrvCtrl->ngbRxDescMem[pDrvCtrl->ngbRxIdx];
            continue;
            }

    /* Sync the packet buffer and unload the map. */
#ifdef NGB_VXB_DMA_BUF
        vxbDmaBufSync (pDev, pDrvCtrl->ngbMblkTag,
                             pMap, VXB_DMABUFSYNC_PREREAD);
        vxbDmaBufMapUnload (pDrvCtrl->ngbMblkTag, pMap);
#else
       /* pre-invalidate the replacement buffer */
        CACHE_USER_INVALIDATE (pNewMblk->m_data, pNewMblk->m_len);
#endif

        /* Swap the mBlks. */
        pDrvCtrl->ngbRxMblk[pDrvCtrl->ngbRxIdx] = pNewMblk;
        pNewMblk->m_next = NULL;
        /* Note, the data space lengths in pNewMblk were set by
           endPoolTupleGet(). */
        NGB_ADJ (pNewMblk);

#ifdef NGB_VXB_DMA_BUF
        /*
         * Take advantage of the fact that we know there's only
         * going to be one buffer, and use the single buffer map
         * load routine instead of the mBlk load routine.
         */

        vxbDmaBufMapLoad (pDev, pDrvCtrl->ngbMblkTag, pMap,
            pNewMblk->m_data, pNewMblk->m_len, 0);

        pDesc->read.ngb_addrlo = htole32(NGB_ADDR_LO(pMap->fragList[0].frag));
        pDesc->read.ngb_addrhi = htole32(NGB_ADDR_HI(pMap->fragList[0].frag));
#else
        UINT32 addr = (UINT32)pNewMblk->m_data;
        if (cacheUserFuncs.virtToPhysRtn)
            addr = cacheUserFuncs.virtToPhysRtn (addr);
        pDesc->read.ngb_addrlo = htole32(addr);
        pDesc->read.ngb_addrhi = 0;
#endif

        pMblk->m_len = pMblk->m_pkthdr.len =
            le16toh(pDesc->write.ngb_pkt_buflen);
        pMblk->m_flags = M_PKTHDR|M_EXT;

        /* Handle checksum offload. */

        if (pDrvCtrl->ngbCaps.cap_enabled & IFCAP_RXCSUM)
            {
            if (pDesc->write.ngb_sts & htole16(NGB_ADV_RDESC_STS_IPCS))
                pMblk->m_pkthdr.csum_flags |= CSUM_IP_CHECKED;
            if (!(pDesc->write.ngb_err & htole16(NGB_ADV_RDESC_ERRSTS_IPE)))
                pMblk->m_pkthdr.csum_flags |= CSUM_IP_VALID;
            if ((pDesc->write.ngb_sts & htole16(NGB_ADV_RDESC_STS_UDPCV)) &&
                !(pDesc->write.ngb_err & htole16(NGB_ADV_RDESC_ERR_TCPE)))
                {
                pMblk->m_pkthdr.csum_flags |= CSUM_DATA_VALID|CSUM_PSEUDO_HDR;
                pMblk->m_pkthdr.csum_data = 0xffff;
                }
            }

        /* Handle VLAN tags */

        if (pDrvCtrl->ngbCaps.cap_enabled & IFCAP_VLAN_HWTAGGING)
           {
           if (pDesc->write.ngb_sts & htole32(NGB_ADV_RDESC_STS_VP))
               {
                pMblk->m_pkthdr.csum_flags |= CSUM_VLAN;
                pMblk->m_pkthdr.vlan = le16toh(pDesc->write.ngb_vlan);
               }
           }

    /* Reset this descriptor's status fields. */

        pDesc->read.ngb_hdrlo = 0;
        pDesc->read.ngb_hdrhi = 0;

        /* Advance to the next descriptor */
        CACHE_PIPE_FLUSH();
        CSR_WRITE_4(pDev, NGB_PX_RR_WP(0), pDrvCtrl->ngbRxIdx);
        NGB_INC_DESC(pDrvCtrl->ngbRxIdx, NGB_RX_DESC_CNT);
        loopCounter--;

        END_RCV_RTN_CALL (&pDrvCtrl->ngbEndObj, pMblk);
        pDesc = (NGB_ADV_RDESC *)&pDrvCtrl->ngbRxDescMem[pDrvCtrl->ngbRxIdx];
        }
    return loopCounter;
    }

/******************************************************************************
*
* ngbEndTbdClean - clean the TX ring
*
* This function is called from both ngbEndSend() or ngbEndTxHandle() to
* reclaim TX descriptors, and to free packets and DMA maps associated
* with completed transmits.  This routine must be called with the END
* TX semaphore already held.
*
* RETURNS: N/A
*
* ERRNO: N/A
*/

LOCAL void ngbEndTbdClean
    (
    NGB_DRV_CTRL * pDrvCtrl
    )
    {
    VXB_DEVICE_ID pDev;
#ifdef NGB_VXB_DMA_BUF
    VXB_DMA_MAP_ID pMap;
#endif
    NGB_ADV_TDESC * pDesc;
    M_BLK_ID pMblk;

    pDev = pDrvCtrl->ngbDev;

    while (pDrvCtrl->ngbTxFree < NGB_TX_DESC_CNT)
        {

        pDesc = &pDrvCtrl->ngbTxDescMem[pDrvCtrl->ngbTxCons];

        if ((pDesc->ngb_sts & htole32(NGB_ADV_STS_DD)) == 0)
            {
            break;
            }
        pMblk = pDrvCtrl->ngbTxMblk[pDrvCtrl->ngbTxCons];
#ifdef NGB_VXB_DMA_BUF
        pMap = pDrvCtrl->ngbTxMblkMap[pDrvCtrl->ngbTxCons];
#endif

        if (pMblk != NULL)
            {
#ifdef NGB_VXB_DMA_BUF
            vxbDmaBufMapUnload (pDrvCtrl->ngbMblkTag, pMap);
#endif
            endPoolTupleFree (pMblk);
            pDrvCtrl->ngbTxMblk[pDrvCtrl->ngbTxCons] = NULL;
            }

        pDrvCtrl->ngbTxFree++;
        NGB_INC_DESC (pDrvCtrl->ngbTxCons, NGB_TX_DESC_CNT);

        pDesc->ngb_addrhi = 0;
        }

    return;
    }

/******************************************************************************
*
* ngbEndTxHandle - process TX completion events
*
* This function is scheduled by the ISR to run in the context of tNetTask
* whenever an TX interrupt is received. It runs through all of the
* TX register pairs and checks the TX status to see how many have
* completed. For each completed transmission, the associated TX mBlk
* is released, and the outbound packet stats are updated.
*
* In the event that a TX underrun error is detected, the TX FIFO
* threshold is increased. This will continue until the maximum TX
* FIFO threshold is reached.
*
* If the transmitter has stalled, this routine will also call muxTxRestart()
* to drain any packets that may be waiting in the protocol send queues,
*
* RETURNS: N/A
*
* ERRNO: N/A
*/

LOCAL void ngbEndTxHandle
    (
    NGB_DRV_CTRL *pDrvCtrl
    )
    {
    UINT32 origFree;

    END_TX_SEM_TAKE (&pDrvCtrl->ngbEndObj, WAIT_FOREVER);
    origFree = pDrvCtrl->ngbTxFree;
    ngbEndTbdClean (pDrvCtrl);
    if (pDrvCtrl->ngbTxStall && origFree < pDrvCtrl->ngbTxFree)
        {
        pDrvCtrl->ngbTxStall = FALSE;
        origFree = ~0U; /* flag that we should TX restart */
        }
    END_TX_SEM_GIVE (&pDrvCtrl->ngbEndObj);

    if (origFree == ~0U)
        muxTxRestart (pDrvCtrl);

    return;
    }

/******************************************************************************
*
* ngbEndEncap - encapsulate an outbound packet using advanced descriptors
*
* This function sets up a descriptor for a packet transmit operation.
* With the WangXun ethernet controller, the TX DMA ring consists of
* descriptors that each describe a single packet fragment. We consume
* as many descriptors as there are mBlks in the outgoing packet, unless
* the chain is too long. The length is limited by the number of DMA
* segments we want to allow in a given DMA map. If there are too many
* segments, this routine will fail, and the caller must coalesce the
* data into fewer buffers and try again.
*
* The WangXun also has context descriptors that are used to set
* up what kind of checksum offload will be used for the frames that
* follow. One context descriptor is required whenever the frame payload
* changes between UDP and TCP.
*
* This routine will also fail if there aren't enough free descriptors
* available in the ring, in which case the caller must defer the
* transmission until more descriptors are completed by the chip.
*
* RETURNS: ENOSPC if there are too many fragments in the packet, EAGAIN
* if the DMA ring is full, otherwise OK.
*
* ERRNO: N/A
*/

LOCAL int ngbEndEncap
    (
    NGB_DRV_CTRL * pDrvCtrl,
    M_BLK_ID pMblk
    )
    {
    VXB_DEVICE_ID pDev;
    NGB_ADV_TDESC * pDesc = NULL;
    UINT32 firstIdx, lastIdx = 0;
#ifdef NGB_VXB_DMA_BUF
    int i;
    VXB_DMA_MAP_ID pMap;
#else
    M_BLK * pTmp;
    UINT32 nFrags;
    UINT32 addr;
#endif

    pDev = pDrvCtrl->ngbDev;
    firstIdx = pDrvCtrl->ngbTxProd;

#ifdef NGB_VXB_DMA_BUF
        pMap = pDrvCtrl->ngbTxMblkMap[pDrvCtrl->ngbTxProd];
#endif

#ifdef NGB_VXB_DMA_BUF
    pMap = pDrvCtrl->ngbTxMblkMap[pDrvCtrl->ngbTxProd];

    /*
     * Load the DMA map to build the segment list.
     * This will fail if there are too many segments.
     */

    if (vxbDmaBufMapMblkLoad (pDev, pDrvCtrl->ngbMblkTag,
        pMap, pMblk, 0) != OK || (pMap->nFrags > pDrvCtrl->ngbTxFree))
        {
        vxbDmaBufMapUnload (pDrvCtrl->ngbMblkTag, pMap);
        return (ENOSPC); /* XXX what about the context descriptor? */
        }

    for (i = 0; i < pMap->nFrags; i++)
        {
        pDesc = (NGB_ADV_TDESC *)&pDrvCtrl->ngbTxDescMem[pDrvCtrl->ngbTxProd];
#ifdef NGB_64
        pDesc->ngb_addrlo = htole32(NGB_ADDR_LO(pMap->fragList[i].frag));
        pDesc->ngb_addrhi = htole32(NGB_ADDR_HI(pMap->fragList[i].frag));
#else
        pDesc->ngb_addrlo = htole32((UINT32)pMap->fragList[i].frag);
#endif

        pDesc->ngb_cmd = htole32(NGB_ADV_TDESC_DTYP_DSC|NGB_ADV_TDESC_CMD_RS|NGB_ADV_TDESC_CMD_IFCS);
        pDesc->ngb_cmd |= htole32(pMap->fragList[i].fragLen);
        pDesc->ngb_sts = 0;

        if (i == 0)
            {
            pDesc->ngb_sts |=
                htole32(NGB_DESC_STATUS_PAYLEN(pMblk->m_pkthdr.len));
            }
        pDrvCtrl->ngbTxFree--;
        lastIdx = pDrvCtrl->ngbTxProd;
        NGB_INC_DESC(pDrvCtrl->ngbTxProd, NGB_TX_DESC_CNT);
        }

#else /* NGB_VXB_DMA_BUF */
    nFrags = 0;
    for (pTmp = pMblk; pTmp != NULL; pTmp = pTmp->m_next)
        {
        if (pTmp->m_len != 0)
            ++nFrags;
        }
    if (nFrags >= pDrvCtrl->ngbTxFree)
        {
        return ENOSPC;
        }
    nFrags = 0;
    for (pTmp = pMblk; pTmp != NULL; pTmp = pTmp->m_next)
        {
        UINT32 cmd;

        if ((cmd = pTmp->m_len) == 0)
            continue;
        pDesc = (NGB_ADV_TDESC *)&pDrvCtrl->ngbTxDescMem[pDrvCtrl->ngbTxProd];

        addr = (UINT32)pTmp->m_data;
        CACHE_USER_FLUSH (addr, cmd);
        if (cacheUserFuncs.virtToPhysRtn)
            addr = cacheUserFuncs.virtToPhysRtn (addr);
        pDesc->ngb_addrlo = htole32(addr);

        cmd |= NGB_ADV_TDESC_DTYP_DSC|NGB_ADV_TDESC_CMD_IFCS|
            NGB_ADV_TDESC_CMD_RS;

        pDesc->ngb_cmd = htole32(cmd);
        pDesc->ngb_sts = 0;

        if (nFrags++ == 0)
            {
        pDesc->ngb_sts |=
                htole32(NGB_DESC_STATUS_PAYLEN(pMblk->m_pkthdr.len));
            }
        pDrvCtrl->ngbTxFree--;
        lastIdx = pDrvCtrl->ngbTxProd;
        NGB_INC_DESC(pDrvCtrl->ngbTxProd, NGB_TX_DESC_CNT);
        }

#endif /* NGB_VXB_DMA_BUF */

        pDesc->ngb_cmd |= htole32(NGB_ADV_TDESC_CMD_EOP|
                                  NGB_ADV_TDESC_CMD_RS);

    /* Save the mBlk for later. */

        pDrvCtrl->ngbTxMblk[lastIdx] = pMblk;

#ifdef NGB_VXB_DMA_BUF
        /*
         * Ensure that the map for this transmission
         * is placed at the array index of the last descriptor
         * in this chain.  (Swap last and first dmamaps.)
         */

        pDrvCtrl->ngbTxMblkMap[firstIdx] = pDrvCtrl->ngbTxMblkMap[lastIdx];
        pDrvCtrl->ngbTxMblkMap[lastIdx] = pMap;

        /* Sync the buffer. */

        vxbDmaBufSync (pDev, pDrvCtrl->ngbMblkTag, pMap, VXB_DMABUFSYNC_POSTWRITE);
#endif

    return (OK);
    }

/******************************************************************************
*
* ngbdSend - transmit a packet
*
* This function transmits the packet specified in <pkt>.
*
* RETURNS: OK, ERROR, or END_ERR_BLOCK.
*
* ERRNO: N/A
*/

LOCAL int ngbEndSend
    (
    END_OBJ * pEnd,
    M_BLK_ID pMblk
    )
    {
    NGB_DRV_CTRL * pDrvCtrl;
    VXB_DEVICE_ID pDev;
    M_BLK_ID pTmp;
    int rval;

    pDrvCtrl = (NGB_DRV_CTRL *)pEnd;

    pDev = pDrvCtrl->ngbDev;

    END_TX_SEM_TAKE (pEnd, WAIT_FOREVER);

    if (!pDrvCtrl->ngbTxFree || !(pDrvCtrl->ngbCurStatus & IFM_ACTIVE))
        goto blocked;

    /* Attempt early cleanup */

    if (pDrvCtrl->ngbTxFree < NGB_TX_DESC_CNT - NGB_TX_CLEAN_THRESH)
        ngbEndTbdClean (pDrvCtrl);

    rval = ngbEndEncap (pDrvCtrl, pMblk);
    /*
     * If ngbEndEncap() returns ENOSPC, it means it ran out
     * of TX descriptors and couldn't encapsulate the whole
     * packet fragment chain. In that case, we need to
     * coalesce everything into a single buffer and try
     * again. If any other error is returned, then something
     * went wrong, and we have to abort the transmission
     * entirely.
     */

    if (rval == ENOSPC)
        {

        if ((pTmp = endPoolTupleGet (pDrvCtrl->ngbEndObj.pNetPool)) == NULL)
            goto blocked;
        pTmp->m_len = pTmp->m_pkthdr.len =
            netMblkToBufCopy (pMblk, mtod(pTmp, char *), NULL);
        pTmp->m_flags = pMblk->m_flags;
        pTmp->m_pkthdr.csum_flags = pMblk->m_pkthdr.csum_flags;
        pTmp->m_pkthdr.csum_data = pMblk->m_pkthdr.csum_data;
        pTmp->m_pkthdr.vlan = pMblk->m_pkthdr.vlan;
        CSUM_IP_HDRLEN(pTmp) = CSUM_IP_HDRLEN(pMblk);
#ifdef CSUM_IPHDR_OFFSET
        CSUM_IPHDR_OFFSET(pTmp) = CSUM_IPHDR_OFFSET(pMblk);
#endif

        /* Try transmission again, should succeed this time. */
        rval = ngbEndEncap (pDrvCtrl, pTmp);
        if (rval == OK)
            endPoolTupleFree (pMblk);
        else
            endPoolTupleFree (pTmp);
        }

    /* Issue transmit command */
    CACHE_PIPE_FLUSH();
    CSR_WRITE_4(pDev, NGB_PX_TR_WP(0), pDrvCtrl->ngbTxProd);
    if (rval != OK)
            goto blocked;

    END_TX_SEM_GIVE (pEnd);

    return (0);

blocked:

    pDrvCtrl->ngbTxStall = TRUE;
    END_TX_SEM_GIVE (pEnd);

    return (END_ERR_BLOCK);
    }

/******************************************************************************
*
* ngbEndPollSend - polled mode transmit routine
*
* This function is similar to the ngbEndSend() routine shown above, except
* it performs transmissions synchronously with interrupts disabled. After
* the transmission is initiated, the routine will poll the state of the
* TX status register associated with the current slot until transmission
* completed. If transmission times out, this routine will return ERROR.
*
* RETURNS: OK, EAGAIN, or ERROR
*
* ERRNO: N/A
*/

LOCAL STATUS ngbEndPollSend
    (
    END_OBJ * pEnd,
    M_BLK_ID pMblk
    )
    {
    NGB_DRV_CTRL * pDrvCtrl;
    VXB_DEVICE_ID pDev;
#ifdef NGB_VXB_DMA_BUF
    VXB_DMA_MAP_ID pMap;
#endif
    NGB_ADV_TDESC * pDesc;
    M_BLK_ID pTmp;
    int len, i;

    pDrvCtrl = (NGB_DRV_CTRL *)pEnd;

    if (pDrvCtrl->ngbPolling == FALSE)
        return (ERROR);

    pDev = pDrvCtrl->ngbDev;
    pTmp = pDrvCtrl->ngbPollBuf;

    len = netMblkToBufCopy (pMblk, mtod(pTmp, char *), NULL);
    pTmp->m_len = pTmp->m_pkthdr.len = len;
    pTmp->m_flags = pMblk->m_flags;
    pTmp->m_pkthdr.csum_flags = pMblk->m_pkthdr.csum_flags;
    pTmp->m_pkthdr.csum_data = pMblk->m_pkthdr.csum_data;
    pTmp->m_pkthdr.vlan = pMblk->m_pkthdr.vlan;
    CSUM_IP_HDRLEN(pTmp) = CSUM_IP_HDRLEN(pMblk);

    /* Encapsulate buffer */

    if (ngbEndEncap (pDrvCtrl, pTmp) != OK)
        return (EAGAIN);

    /* Issue transmit command */

    CSR_WRITE_4(pDev, NGB_PX_TR_RP(0), pDrvCtrl->ngbTxProd);

    /* Wait for transmission to complete */

    for (i = 0; i < NGB_TIMEOUT; i++)
        {
        if (CSR_READ_4(pDev, NGB_PX_TR_WP(0)) == CSR_READ_4(pDev, NGB_PX_TR_RP(0)))
            break;
        }

    CSR_WRITE_4(pDev, NGB_PX_IMC(0), 0x00000001);

    /* Remember to unload the map once transmit completes. */

    FOREVER
        {
        pDesc = &pDrvCtrl->ngbTxDescMem[pDrvCtrl->ngbTxCons];
        pDesc->ngb_addrhi = 0;
        pTmp = pDrvCtrl->ngbTxMblk[pDrvCtrl->ngbTxCons];
        if (pDrvCtrl->ngbTxMblk[pDrvCtrl->ngbTxCons] != NULL)
            {
#ifdef NGB_VXB_DMA_BUF
            pMap = pDrvCtrl->ngbTxMblkMap[pDrvCtrl->ngbTxCons];
            vxbDmaBufMapUnload (pDrvCtrl->ngbMblkTag, pMap);
#endif
            pDrvCtrl->ngbTxMblk[pDrvCtrl->ngbTxCons] = NULL;
            }
        pDrvCtrl->ngbTxFree++;
        NGB_INC_DESC(pDrvCtrl->ngbTxCons, NGB_TX_DESC_CNT);
        if (pDesc->ngb_cmd & htole32(NGB_ADV_TDESC_CMD_EOP))
            break;
        }

    if (i == NGB_TIMEOUT)
        return (ERROR);

    return (OK);

    }

/******************************************************************************
*
* ngbEndPollReceive - polled mode receive routine
*
* This function receive a packet in polled mode, with interrupts disabled.
* It's similar in operation to the ngbEndRxHandle() routine, except it
* doesn't process more than one packet at a time and does not load out
* buffers. Instead, the caller supplied an mBlk tuple into which this
* function will place the received packet.
*
* If no packet is available, this routine will return EAGAIN. If the
* supplied mBlk is too small to contain the received frame, the routine
* will return ERROR.
*
* RETURNS: OK, EAGAIN, or ERROR
*
* ERRNO: N/A
*/

LOCAL int ngbEndPollReceive
    (
    END_OBJ * pEnd,
    M_BLK_ID pMblk
    )
    {
    NGB_DRV_CTRL * pDrvCtrl;
    VXB_DEVICE_ID pDev;
#ifdef NGB_VXB_DMA_BUF
    VXB_DMA_MAP_ID pMap;
#endif
    NGB_ADV_RDESC * pDesc;
    M_BLK_ID pPkt;
    int rval = EAGAIN;
    UINT16 rxLen = 0;

    pDrvCtrl = (NGB_DRV_CTRL *)pEnd;
    if (pDrvCtrl->ngbPolling == FALSE)
        return (ERROR);
    if (!(pMblk->m_flags & M_EXT))
        return (ERROR);

    pDev = pDrvCtrl->ngbDev;

    pDesc = (NGB_ADV_RDESC *)&pDrvCtrl->ngbRxDescMem[pDrvCtrl->ngbRxIdx];

    if (!(pDesc->write.ngb_sts & htole16(NGB_ADV_RDESC_STS_DD)))
        return (EAGAIN);

    CSR_WRITE_4(pDev, NGB_PX_IMC(0), 0x00000001);

    pPkt = pDrvCtrl->ngbRxMblk[pDrvCtrl->ngbRxIdx];

#ifdef NGB_VXB_DMA_BUF
    pMap = pDrvCtrl->ngbRxMblkMap[pDrvCtrl->ngbRxIdx];
#endif
    rxLen = le16toh(pDesc->write.ngb_pkt_buflen);

#ifndef NGB_SECRC
    rxLen -= 4;
#endif

#ifdef NGB_VXB_DMA_BUF
    vxbDmaBufSync (pDev, pDrvCtrl->ngbMblkTag, pMap, VXB_DMABUFSYNC_PREREAD);
#endif

    /*
     * Ignore checksum errors here. They'll be handled by the
     * checksum offload code below, or by the stack.
     */

    if (!(pDesc->write.ngb_err & htole16(NGB_RXD_ERR_RXE)))
        {
        /* Handle checksum offload. */

        if (pDrvCtrl->ngbCaps.cap_enabled & IFCAP_RXCSUM)
            {
            if ((pDesc->write.ngb_sts & htole16(NGB_ADV_RDESC_STS_UDPCV)) &&
                !(pDesc->write.ngb_err & htole16(NGB_ADV_RDESC_ERR_TCPE)))
                {
                pMblk->m_pkthdr.csum_flags |= CSUM_DATA_VALID|CSUM_PSEUDO_HDR;
                pMblk->m_pkthdr.csum_data = 0xffff;
                }
            }

        /* Handle VLAN tags */

        if (pDrvCtrl->ngbCaps.cap_enabled & IFCAP_VLAN_HWTAGGING)
           {
           if (pDesc->write.ngb_sts & htole16(NGB_ADV_RDESC_STS_VP))
               {
                pMblk->m_pkthdr.csum_flags |= CSUM_VLAN;
                pMblk->m_pkthdr.vlan = le16toh(pDesc->write.ngb_vlan);
               }
           }

        pMblk->m_flags |= M_PKTHDR;
        pMblk->m_len = pMblk->m_pkthdr.len = rxLen;
        NGB_ADJ(pMblk);
        bcopy (mtod(pPkt, char *), mtod(pMblk, char *), rxLen);
        rval = OK;
        }

#ifdef NGB_VXB_DMA_BUF
        /*
         * Pre-invalidate the re-used buffer. Note that pPkt->m_len
         * was set by endPoolTupleGet(), and hasn't been changed,
         * but pPkt->m_data was adjusted up by 2 bytes.
         */
        CACHE_DMA_INVALIDATE (pPkt->m_data - 2, pPkt->m_len);
#endif


    /* Reset the descriptor */

#ifdef NGB_VXB_DMA_BUF
    pDesc->read.ngb_addrlo = htole32(NGB_ADDR_LO(pMap->fragList[0].frag));
    pDesc->read.ngb_addrhi = htole32(NGB_ADDR_HI(pMap->fragList[0].frag));
#else
    {
    UINT32 addr = (UINT32)pPkt->m_data;
    if (cacheUserFuncs.virtToPhysRtn)
        addr = cacheUserFuncs.virtToPhysRtn (addr);
    pDesc->read.ngb_addrlo = htole32(addr);
    pDesc->read.ngb_addrhi = 0;
    }
#endif
    pDesc->read.ngb_hdrhi = 0;
    pDesc->read.ngb_hdrlo = 0;

    CSR_WRITE_4(pDev, NGB_PX_RR_RP(0), pDrvCtrl->ngbRxIdx);
    NGB_INC_DESC(pDrvCtrl->ngbRxIdx, NGB_RX_DESC_CNT);

    return (rval);

    }

void ngbmuxconnect()
    {
        vxbDevMethodRun((UINT32)muxDevConnect_desc,NULL);
    }


void ngbprint()
{
    logMsg("ngb print: print reg start \n",0,1,2,3,4,5);

/**/
#if 1
    VXB_DEVICE_ID pDev;
    NGB_DRV_CTRL * ppDrvCtrl;

    pDev = pDev0;
    pDev = pDev1;
    pDev = pDev2;
    pDev = pDev3;

    ppDrvCtrl = pDev->pDrvCtrl;
#if 0
    NGB_ADV_TDESC * pptDesc;
    NGB_ADV_RDESC * pprDesc;

    UINT16 value =0;
    UINT32 ret;
    int i;
#endif
#endif

    logMsg("ngb print: print reg start \n",0,1,2,3,4,5);

#if 0
    ngbPhyRead(pDev, 16, 0xd04, &value);
    logMsg("value16 is 0x%0x \n", value, 1,2,3,4,5);
    ngbPhyRead(pDev, 17, 0xd04, &value);
    logMsg("value17 is 0x%0x \n", value, 1,2,3,4,5);
    ngbPhyRead(pDev, 18, 0xd04, &value);
    logMsg("value18 is 0x%0x \n", value, 1,2,3,4,5);
    ngbPhyRead(pDev, 0, 0, &value);
    logMsg("value00 is 0x%0x \n", value, 1,2,3,4,5);
    ngbPhyRead(pDev, 4, 0, &value);
    logMsg("value00 is 0x%0x \n", value, 1,2,3,4,5);
#endif
#if 0
    value = 0x205B;
    ngbPhyWrite(pDev, 16, 0xd04, value);

    ngbPhyRead(pDev, 16, 0xd04, &value);
    logMsg("value16-1 is 0x%0x \n", value, 1,2,3,4,5);
#endif

/* Rx、Tx count print */
#if 0
    /**rx path**/
    ret = CSR_READ_4(pDev, 0x11900);
    logMsg("print:  mac rx is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x151B8);
    logMsg("print:  PSR rx is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x19060);
    logMsg("print:  RDB rx is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x12504);
    logMsg("print:  rdma rx is 0x%0x\n",ret,1,2,3,4,5);

    /**tx path*/
    ret = CSR_READ_4(pDev, 0x18308);
    logMsg("print:  tdma tx is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1CF00);
    logMsg("print:  tdB tx is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1181C);
    logMsg("print:  MAC tx is 0x%0x\n",ret,1,2,3,4,5);
#endif

/* Desc info print */
#if 0
    pDesc->write.ngb_sts	
    pprDesc = (NGB_ADV_RDESC *)&ppDrvCtrl->ngbRxDescMem[0];
    pptDesc = (NGB_ADV_TDESC *)&ppDrvCtrl->ngbTxDescMem[0];

    for (i=0;i<128;i++)
        {
        pprDesc = (NGB_ADV_RDESC *)&ppDrvCtrl->ngbRxDescMem[i];
        logMsg("pprDesc[%d].write:ngb_sts is 0x%0x \n",i,pprDesc->write.ngb_sts,2,3,4,5);
        }

    for (i=0;i<6;i++)
        {
        pptDesc = (NGB_ADV_TDESC *)&ppDrvCtrl->ngbTxDescMem[i];
        logMsg("pptDesc[%d]->addrlo:0x%0x, addrhi:0x%0x, cmd:0x%0x, sts:0x%0x\n",
        i, pptDesc->ngb_addrlo, pptDesc->ngb_addrhi, pptDesc->ngb_cmd, pptDesc->ngb_sts, 5);
        }
#endif


/* Phy reg print */
#if 0
    ngbPhyRead(pDev, 0x14, 0xa46, &value);
    ngbPhyRead(pDev, 0x1d, 0xa43, &value);
    ngbPhyRead(pDev, 29, 0xA43, &value);
    value &= 0x4;
    logMsg("print: 29, 0xa43 value is 0x%0x \n",value,1,2,3,4,5);
#endif


/* Config reg print */
#if 0
    ret = CSR_READ_4(pDev, NGB_PX_IMS(0));
    logMsg("print: NGB_PX_IMS(0) is 0x%0x \n",ret,1,2,3,4,5);

    ret = CSR_READ_4(pDev, NGB_PX_RR_BAH(0));
    logMsg("print: NGB_PX_RR_BAH(0) is 0x%0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, NGB_PX_RR_BAL(0));
    logMsg("print: NGB_PX_RR_BAL(0) is 0x%0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, NGB_PX_RR_CFG(0)); 
    logMsg("print: NGB_PX_RR_CFG(0) is 0x%0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, NGB_PX_TR_CFG(0));
    logMsg("print: NGB_PX_TR_CFG(0) is 0x%0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, NGB_PX_TR_BAH(0));
    logMsg("print: NGB_PX_TR_BAH(0) is 0x%0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, NGB_PX_TR_BAL(0));
    logMsg("print: NGB_PX_TR_BAL(0) is 0x%0x \n",ret,1,2,3,4,5);

    ret = CSR_READ_4(pDev, 0x16200);
    logMsg("print: mac addr_l is 0x%0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x16204);
    logMsg("print: mac addr_h is 0x%0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, NGB_PX_TR_WP(0));
    logMsg("print: NGB_PX_TR_WP is 0x%0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, NGB_PX_TR_RP(0));
    logMsg("print: NGB_PX_TR_RP is 0x%0x \n",ret,1,2,3,4,5);

    ret = CSR_READ_4(pDev, NGB_PSR_CTL);
    logMsg("print: NGB_PSR_CTL is 0x%0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, NGB_MAC_TX_CFG);
    logMsg("print: NGB_MAC_TX_CFG is 0x%0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, NGB_MAC_RX_CFG);
    logMsg("print: NGB_MAC_RX_CFG is 0x%0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, NGB_RDB_PB_CTL);
    logMsg("print: NGB_RDB_PB_CTL is 0x%0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, NGB_CFG_PORT_CTL);
    logMsg("print: NGB_CFG_PORT_CTL is 0x%0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, NGB_RSC_CTL);
    logMsg("print: NGB_RSC_CTL is %0x \n",ret,1,2,3,4,5);

    ret = CSR_READ_4(pDev, NGB_PX_IVAR0);
    logMsg("print: NGB_PX_IVAR0is %0x \n",ret,1,2,3,4,5);

    ret = CSR_READ_4(pDev, NGB_PX_MISC_IVAR);
    logMsg("print: NGB_PX_MISC_IVAR is %0x \n",ret,1,2,3,4,5);

    ret = CSR_READ_4(pDev, NGB_PX_ITR0);
    logMsg("print: NGB_PX_ITR0 is %0x \n",ret,1,2,3,4,5);	
    ret = CSR_READ_4(pDev, NGB_PX_ISB_ADDR_L);
    logMsg("print: NGB_PX_ISB_ADDR_L is 0x%0x\n",ret,1,2,3,4,5);	
    ret = CSR_READ_4(pDev, NGB_PX_ISB_ADDR_H);
    logMsg("print: NGB_PX_ISB_ADDR_H is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1CC00);
    logMsg("print: TDB_PB_SZ 0x1CC00 is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, NGB_PX_MISC_IEN);
    logMsg("print: NGB_PX_MISC_IEN is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, NGB_PX_IMC(0));
    logMsg("print: NGB_PX_IMC(0) is 0x%0x\n",ret,1,2,3,4,5);
#endif


/* Tx debug print reg after 0x3000 */
#if 0
    logMsg("print:	0x3000 dump start:\n",ret,1,2,3,4,5);	
    ret = CSR_READ_4(pDev, 0x3000);
    logMsg("print:	0x3000 TR_BAL is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x3004);
    logMsg("print:	0x3004 TR_BAH is: 0x%0x\n",ret,1,2,3,4,5);

    ret = CSR_READ_4(pDev, 0x3008);
    logMsg("print:	0x3008 TR_WP is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x300C);
    logMsg("print:	0x300C TR_RP is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x3010);
    logMsg("print:	0x3010 TR_cfg is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x3014);
    logMsg("print:	0x3014 Px_GPTC is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x3018);
    logMsg("print:	0x3018 Px_GOTC_LSB is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x301C);
    logMsg("print:	0x301C Px_GOTC_MSB is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x3020);
    logMsg("print:	0x3020 Px_MPTC is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x3024);
    logMsg("print:	0x3024 Px_BPTC is: 0x%0x\n",ret,1,2,3,4,5);
#endif


/* Tx debug print reg after 0x8000 */
#if 0
    logMsg("print:	0x8000 dump start:\n",ret,1,2,3,4,5);	
    ret = CSR_READ_4(pDev, 0x18000);
    logMsg("print:	0x18000 Tdm_ctl is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x18010);
    logMsg("print:	0x18010 Tdm_prb_ctl is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x18014);
    logMsg("print:	0x18014 Tdm_err_ctl is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x18018);
    logMsg("print:	0x18018 Tdm_err_inj is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1801C);
    logMsg("print:	0x1801C Tdm_err_st is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x18020);
    logMsg("print:	0x18020 Tdm_pb_thre is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x18040);
    logMsg("print:	0x18040 Tdm_llq is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x18050);
    logMsg("print:	0x18050 Tdm_etype_lb_l is: 0x%0x\n",ret,1,2,3,4,5);
#endif


/* Tsc、rsc reg print */
#if 0
    ret = CSR_READ_4(pDev, 0x17000);
    logMsg("print:	0x17000  is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1cd00);
    logMsg("print:	0x1cd00  is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1d000);
    logMsg("print:	0x1d000 tsc_ctl  is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1d004);
    logMsg("print:	0x1d004 tsc_st  is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1d008);
    logMsg("print:	0x1d008 tsc_buf_af  is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1d00c);
    logMsg("print:	0x1d00c tsc_buf_ae  is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1d010);
    logMsg("print:	0x1d010 tsc_prb_ctl  is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1d014);
    logMsg("print:	0x1d014 tsc_ecc_ctl  is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1d018);
    logMsg("print:	0x1d018 tsc_ecc_inj  is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1d01c);
    logMsg("print:	0x1d01c tsc_ecc_st  is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1d020);
    logMsg("print:	0x1d020 tsc_min_ifg  is: 0x%0x\n",ret,1,2,3,4,5);
#endif


/* Mac tx reg print */
#if 0
    ret = CSR_READ_4(pDev, 0x11000);
    logMsg("print:	0x11000 mac_tx_cfg is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x11004);
    logMsg("print:	0x11004 mac_rx_cfg is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x11008);
    logMsg("print:	0x11008 mac_pkt_filter is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1100c);
    logMsg("print:	0x1100c mac_wdg_timeout is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x11010);
    logMsg("print:	0x11010 mac_tx_prb_ctl is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x11014);
    logMsg("print:	0x11014 mac_rx_prb_ctl is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x11070);
    logMsg("print:	0x11070 mac_tx_flow_ctl is: 0x%0x\n",ret,1,2,3,4,5);

    ret = CSR_READ_4(pDev, 0x110B0);
    logMsg("print:	0x110B0 mac_intr_status is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x110B4);
    logMsg("print:	0x110B4 mac_intr_enable is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x110B8);
    logMsg("print:	0x110B8 mac_rx_tx_status is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x110D0);
    logMsg("print:	0x110d0 mac_lpi_ctl_status is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x110D4);
    logMsg("print:	0x110d4 mac_lpi_timer_status is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x11114);
    logMsg("print:	0x11114 mac_debug is: 0x%0x\n",ret,1,2,3,4,5);
#endif


/* Interrupt reg print */
#if 0
    ret = CSR_READ_4(pDev, 0x100);
    logMsg("print:	0x100 Px_misc_ic is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x104);
    logMsg("print:	0x104 Px_misc_ics is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x108);
    logMsg("print:	0x108 Px_misc_ien is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x118);
    logMsg("print:	0x118 Px_misc_ics is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x120);
    logMsg("print:	0x120 Px_ic0 is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x130);
    logMsg("print:	0x130 Px_ics0 is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x140);
    logMsg("print:	0x140 Px_ims0 is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x150);
    logMsg("print:	0x150 Px_imc0 is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x160);
    logMsg("print:	0x160 Px_isb_addr_l is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x164);
    logMsg("print:	0x160 Px_isb_addr_h is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x200);
    logMsg("print:	0x200 Px_itr0 is: 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x500);
    logMsg("print: 0x500 Px_ivar0 is: %0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x4fc);
    logMsg("print: 0x4fc Px_misc_ivar is %0x \n",ret,1,2,3,4,5);
#endif


/* Rdma reg print */
#if 0
    ret = CSR_READ_4(pDev, 0x12000);
    logMsg("print: 0x12000 Rdm_arb_ctl is %0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x12004);
    logMsg("print: 0x12004 Rdm_vf_re is %0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x12010);
    logMsg("print: 0x12010 Rdm_prb_ctl is %0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x12014);
    logMsg("print: 0x12014 Rdm_ecc_ctl is %0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x12018);
    logMsg("print: 0x12018 Rdm_ecc_inj is %0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1201c);
    logMsg("print: 0x1201c Rdm_ecc_st is %0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x12020);
    logMsg("print: 0x12020 Rdm_bme_ctl is %0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x120a0);
    logMsg("print: 0x120a0 Rdm_vfre_clr is %0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x12080);
    logMsg("print: 0x12080 Rdm_pf_qde is %0x \n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x12090);
    logMsg("print: 0x12090 Rdm_pf_qde is %0x \n",ret,1,2,3,4,5);
#endif


/* pkget count */
#if 0
    ret = CSR_READ_4(pDev, 0x12500);
    logMsg("print:  0X12500 rdm_drp_cnt is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x12504);
    logMsg("print:  0X12504 rdm_pkt_cnt is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x12508);
    logMsg("print:  0X12508 rdm_byte_cnt_l is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1250c);
    logMsg("print:  0X1250c rdm_byte_cnt_h is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x12510);
    logMsg("print:  0X12510 rdm_bmc2os_cnt is 0x%0x\n",ret,1,2,3,4,5);
#endif


/*Other reg print*/
#if 0
    ret = CSR_READ_4(pDev, 0x1484C);
    logMsg("print:  0X1484C is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x14850);
    logMsg("print:  0X14850 is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x14450);
    logMsg("print:  0X14450 is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x13D04);
    logMsg("print:  0X13D04 is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x13D08);
    logMsg("print:  0X13D08 is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x13D0C);
    logMsg("print:  0X13D0C is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x13D18);
    logMsg("print:  0X13D18 is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x13D24);
    logMsg("print:  0X13D24 is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x13D28);
    logMsg("print:  0X13D28 is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x1E084);
    logMsg("print:  0X1E084 is 0x%0x\n",ret,1,2,3,4,5);
    ret = CSR_READ_4(pDev, 0x110B0);
    logMsg("print:  0X110B0 is 0x%0x\n",ret,1,2,3,4,5);
#endif
}
