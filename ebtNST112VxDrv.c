/*************************************************
  Copyright (C),  Embed-Te<PERSON> ,TianJin
  File name:      ebtNST112VxDrv.c
  Author:  		  Version:  V1.0      
  Description:    NST112 Functions  
  
    
*************************************************/
#include <vxWorks.h>
#include "ebtNST112VxDrv.h"

/* debug */
#undef NST112_DBG_ON
#ifdef NST112_DBG_ON
#define NST112_DBG(fmt...)	printf (fmt)
#else
#define NST112_DBG(fmt...)
#endif	/* HK1123_DBG_ON */

#define NST112_NAME "NST112"    

#define Cfg_Extended   	(1 <<  4)	
#define Cfg_Alert    		(1 <<  5)

#define Pointer_Tempe  	0x0
#define Pointer_Cfg  		0x1
#define Pointer_Tlow  		0x2
#define Pointer_Thigh  	0x3

/*unit 3*/

/**
 * @brief nst112Read ��ȡNST112 register
 *
 * @param unit ����NST112�豸��λ ȡֵ��Χ��
 * @param reg  ����ƫ�Ƶ�ַ       ȡֵ��Χ����NULL
 * @param *buf NST112��ȡ��ֵ     ȡֵ��Χ��0-255
 *
 * @return  
 *  ERROR ʧ��
 *  OK �ɹ�   
 *
 */
STATUS nst112Read (UINT8 reg,UINT16 *buf)
{	
	UINT8 add=0;
	UINT8 data[2]={0};

	add=reg;
	
	if((vxbI2cByNameRead2(NST112_NAME,2,&add,1,data,2))!=OK)
	{
		printf("vxbI2cByNameRead2 ERROR!!!\n");
		return ERROR;
	}

	buf[0]=data[1]+((data[0]<<8)&0xff00);
	/*printf(" r  data0=0x%x  data1=0x%x  buf=0x%x\n",data[0],data[1],buf[0]);*/

	return OK;
}



/**
 * @brief nst112Write дNST112
 *
 * @param unit ����NST112�豸��λ ȡֵ��Χ��
 * @param reg ����ƫ�Ƶ�ַ ȡֵ��Χ����NULL
 * @param val NST112д����ֵ ȡֵ��Χ: 0-255
 *
 * @return  
 *  ERROR ʧ��
 *  OK �ɹ� 
 *
 */
STATUS nst112Write(UINT8 reg, UINT16 val)
{
	UINT8 add=0;
	UINT8 data[2]={0};
		
	data[0]=(val>>8)&0xff;
	data[1]=val&0xff;

	add=reg;
	
#if 0	
	printf("  w  reg=0x%x  val=0x%x\n",reg,val);	
	printf("  w  add0=0x%x  add1=0x%x\n",add[0],add[1]);	
	printf("  w  data0=0x%x  data1=0x%x\n",data[0],data[1]);
#endif
	
	if((vxbI2cByNameWrite2(NST112_NAME,2,&add,1,data,2))!=OK)
	{
		printf("vxbI2cByNameWrite2 ERROR!!!\n");
		return ERROR;
	}
	return OK;
}



void nst112ReadTest(UINT8 reg)
{
	UINT16 data=0;
	
	nst112Read(reg,&data);
	printf("data=0x%x\n",data);
}


/**
 * @brief hk1123InitSet ����NST112 �¶�ģʽ
 *
 * @param mode  =  0 ��׼ģʽ��1 ��չģʽ
 * @return  
 *  ERROR ʧ��
 *  OK �ɹ� 
 *
 */
STATUS nst112InitSet(UINT mode)
{
	UINT16 status=0;
	
	if((nst112Read(1,&status))!=OK)
	{
		return ERROR;
	}

	if(mode==0)
		status |=  Cfg_Alert;
	else if(mode==1)
		status |= (Cfg_Extended | Cfg_Alert);

	if((nst112Write(1,status))!=OK)
	{
		return ERROR;
	}	
	
	return OK;
}


/**
 * @brief environmentTemperature ��NST112��׼ģʽ�¶�ȡ�����¶�
 *
 * @param unit ����NST112�豸��λ ȡֵ��Χ��
 * @return  �����¶ȶ�ȡֵ
 *  ����ֵ -100
 *  ��ȷֵ 0-127 
 *
 */

int environmentTemperature(void)
{	
	int data=0;
	UINT16 temp=0,temp1=0;

	if((nst112Read(0x0,&temp))!=OK)
	{
		return ERROR;
	}	

	/*temp=0xCE00;*/
	
	temp1=(temp&0x8000)>>15;

	/*temp1 =1 or = 0  */

	/*printf("temp1=0x%x\n",temp1);*/
	
	if(temp1)
	{
 		data=0-(char)((((~temp)>>4)+1)/16);         /*temperature<0*/
	}
	else
	{
		data=(temp>>4)/16;		/*temperature>=0*/
	}
	/*printf("Temperature= %d \n",data);*/
	return data;
}


/**
 * @brief environmentTemperature ��NST112��չģʽ�¶�ȡ�����¶�
 *
 * @param unit ����NST112�豸��λ ȡֵ��Χ��
 * @return  �����¶ȶ�ȡֵ
 *  ����ֵ -100
 *  ��ȷֵ 0-150 
 *
 */

int environmentTemperatureExt(void)
{	
	int data=0;
	UINT16 temp=0,temp1=0;

	if((nst112Read(0x0,&temp))!=OK)
	{
		return ERROR;
	}	

	/*temp=0xCE00;*/
	
	temp1=(temp&0x8000)>>15;

	/*temp1 =1 or = 0  */

	printf("temp1=0x%x\n",temp1);
	
	if(temp1)
	{
 		data=0-(char)((((~temp)>>3)+1)/16);         /*temperature<0*/
	}
	else
	{
		data=(temp>>3)/16;		/*temperature>=0*/
	}
	printf("Temperature= %d \n",data);
	return data;
}


/**
 * @brief nst112TLowSet ����NST112  Low Limit Registers 
 * @param data  ����Low Limit Registers �¶ȣ� ��λ��
 * @return  
 *  ERROR ʧ��
 *  OK �ɹ� 
 *
 */
STATUS nst112TLowSet(int data)
{
	UINT16 temp=0;


	if(data)
	{
		temp=(UINT16)((data*16)<<4);
	}
	else
	{
		temp=(UINT16)(((0-data)*16-1)<<4);
	}

	printf("temp=0x%x\n",temp);
	
	if((nst112Write(2,temp))!=OK)
	{
		return ERROR;
	}
}

/**
 * @brief nst112THighSet ����NST112  High Limit Registers 
 * @param data  ����High Limit Registers �¶ȣ� ��λ��
 * @return  
 *  ERROR ʧ��
 *  OK �ɹ� 
 *
 */
STATUS nst112THighSet(int data)
{
	UINT16 temp=0;


	if(data)
	{
		temp=(UINT16)((data*16)<<4);
	}
	else
	{
		temp=(UINT16)(((0-data)*16-1)<<4);
	}

	printf("temp=0x%x\n",temp);
	
	if((nst112Write(3,temp))!=OK)
	{
		return ERROR;
	}
}



#if 0
/**
 * @brief nst112InitExternSet ����NST112��չ�¶�ģʽ 
 *
 * @param unit ����NST112�豸��λ ȡֵ��Χ��
 * @return  
 *  ERROR ʧ��
 *  OK �ɹ� 
 *
 */
STATUS nst112InitExternSet(UINT8 unit)
{
	UINT8 status=0;
	
	if((nst112Read(unit,0x03,&status))!=OK)
	{
		return ERROR;
	}
	status=(status|0x84);
	if((nst112Write(unit,0x09,status))!=OK)
	{
		return ERROR;
	}	
	
	return OK;
}

/**
 * @brief cpuTemperatureExtern ��NST112��չģʽ�¶�ȡCPU�¶�
 *
 * @param unit ����NST112�豸��λ ȡֵ��Χ��
 * @return  �����¶ȶ�ȡֵ
 *  ����ֵ -100
 *  ��ȷֵ -64-191 
 *
 */
int cpuTemperatureExtern(UINT8 unit)
{
	UINT8 status=0;
	int value=0;
	
	if((nst112Read(unit,0x1,&status))!=OK)
	{
		return(-100);
	}
	value=(status-64);

    
    return value;
}

/**
 * @brief entironmentTemperatureExtern ��NST112��չģʽ�¶�ȡ�����¶�
 *
 * @param unit ����NST112�豸��λ ȡֵ��Χ��
 * @return  �����¶ȶ�ȡֵ
 *  ����ֵ -100
 *  ��ȷֵ -64-191 
 *
 */
int entironmentTemperatureExtern(UINT8 unit)
{	
	UINT8 status;
	int value=0;

	if((nst112Read(unit,0x0,&status))!=OK)
	{
		return(-100);
	}
	value=(status-64);
	
    return value;
}
#endif
